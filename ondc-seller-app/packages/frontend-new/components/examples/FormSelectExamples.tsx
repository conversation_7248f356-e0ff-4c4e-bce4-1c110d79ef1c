'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Stack,
  Alert,
  Divider,
} from '@mui/material';
import { FormSingleSelect, FormMultiSelect, FormSelect } from '../ui/FormSelect';
import { SelectOption } from '../ui/Select';

// Sample data
const countries: SelectOption[] = [
  { value: 'us', label: 'United States', description: 'North America' },
  { value: 'ca', label: 'Canada', description: 'North America' },
  { value: 'uk', label: 'United Kingdom', description: 'Europe' },
  { value: 'de', label: 'Germany', description: 'Europe' },
  { value: 'fr', label: 'France', description: 'Europe' },
  { value: 'jp', label: 'Japan', description: 'Asia' },
  { value: 'au', label: 'Australia', description: 'Oceania' },
];

const skills: SelectOption[] = [
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'react', label: 'React' },
  { value: 'nodejs', label: 'Node.js' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'csharp', label: 'C#' },
  { value: 'php', label: 'PHP' },
];

const experienceLevels: SelectOption[] = [
  { value: 'junior', label: 'Junior (0-2 years)', description: 'Entry level position' },
  { value: 'mid', label: 'Mid-level (2-5 years)', description: 'Some experience required' },
  { value: 'senior', label: 'Senior (5+ years)', description: 'Extensive experience required' },
  { value: 'lead', label: 'Lead/Principal (8+ years)', description: 'Leadership experience required' },
];

const departments: SelectOption[] = [
  { value: 'engineering', label: 'Engineering' },
  { value: 'design', label: 'Design' },
  { value: 'product', label: 'Product Management' },
  { value: 'marketing', label: 'Marketing' },
  { value: 'sales', label: 'Sales' },
  { value: 'hr', label: 'Human Resources' },
];

// Validation schema
const userProfileSchema = z.object({
  country: z.string().min(1, 'Country is required'),
  skills: z.array(z.string()).min(1, 'At least one skill is required').max(5, 'Maximum 5 skills allowed'),
  experience: z.string().min(1, 'Experience level is required'),
  departments: z.array(z.string()).min(1, 'At least one department is required'),
  primarySkill: z.string().min(1, 'Primary skill is required'),
});

type UserProfileForm = z.infer<typeof userProfileSchema>;

const jobApplicationSchema = z.object({
  position: z.string().min(1, 'Position is required'),
  location: z.string().min(1, 'Location is required'),
  skills: z.array(z.string()).min(3, 'At least 3 skills are required'),
  experience: z.string().min(1, 'Experience level is required'),
  availability: z.string().min(1, 'Availability is required'),
});

type JobApplicationForm = z.infer<typeof jobApplicationSchema>;

const positions: SelectOption[] = [
  { value: 'frontend', label: 'Frontend Developer' },
  { value: 'backend', label: 'Backend Developer' },
  { value: 'fullstack', label: 'Full Stack Developer' },
  { value: 'mobile', label: 'Mobile Developer' },
  { value: 'devops', label: 'DevOps Engineer' },
];

const availability: SelectOption[] = [
  { value: 'immediate', label: 'Immediate' },
  { value: '2weeks', label: '2 Weeks Notice' },
  { value: '1month', label: '1 Month Notice' },
  { value: '3months', label: '3+ Months' },
];

export const FormSelectExamples: React.FC = () => {
  // User Profile Form
  const userProfileForm = useForm<UserProfileForm>({
    resolver: zodResolver(userProfileSchema),
    defaultValues: {
      country: '',
      skills: [],
      experience: '',
      departments: [],
      primarySkill: '',
    },
  });

  // Job Application Form
  const jobApplicationForm = useForm<JobApplicationForm>({
    resolver: zodResolver(jobApplicationSchema),
    defaultValues: {
      position: '',
      location: '',
      skills: ['javascript', 'react'], // Pre-filled
      experience: 'mid', // Pre-selected
      availability: '',
    },
  });

  const onUserProfileSubmit = (data: UserProfileForm) => {
    console.log('User Profile Data:', data);
    alert('User profile submitted successfully! Check console for data.');
  };

  const onJobApplicationSubmit = (data: JobApplicationForm) => {
    console.log('Job Application Data:', data);
    alert('Job application submitted successfully! Check console for data.');
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom sx={{ mb: 4, fontWeight: 600 }}>
        React Hook Form Integration Examples
      </Typography>
      
      <Alert severity="info" sx={{ mb: 4 }}>
        These examples show how to integrate the select components with React Hook Form for validation and form management.
        All forms include Zod validation schemas for type-safe form handling.
      </Alert>

      <Grid container spacing={4}>
        {/* User Profile Form */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                User Profile Form
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Complete form validation with single and multi-select fields
              </Typography>
              
              <form onSubmit={userProfileForm.handleSubmit(onUserProfileSubmit)}>
                <Stack spacing={3}>
                  <FormSingleSelect
                    name="country"
                    control={userProfileForm.control}
                    label="Country *"
                    placeholder="Select your country"
                    options={countries}
                    clearable
                    helperText="Your country of residence"
                  />

                  <FormMultiSelect
                    name="skills"
                    control={userProfileForm.control}
                    label="Technical Skills *"
                    placeholder="Select your skills"
                    options={skills}
                    showSelectAll
                    maxTags={3}
                    helperText="Choose 1-5 skills (max 3 displayed)"
                  />

                  <FormSingleSelect
                    name="experience"
                    control={userProfileForm.control}
                    label="Experience Level *"
                    placeholder="Select your experience level"
                    options={experienceLevels}
                    size="small"
                  />

                  <FormMultiSelect
                    name="departments"
                    control={userProfileForm.control}
                    label="Interested Departments *"
                    placeholder="Select departments"
                    options={departments}
                    chipVariant="outlined"
                    chipColor="secondary"
                  />

                  <FormSingleSelect
                    name="primarySkill"
                    control={userProfileForm.control}
                    label="Primary Skill *"
                    placeholder="Select your primary skill"
                    options={skills}
                    helperText="Your strongest technical skill"
                  />

                  <Divider />

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={userProfileForm.formState.isSubmitting}
                    >
                      Save Profile
                    </Button>
                    <Button
                      type="button"
                      variant="outlined"
                      onClick={() => userProfileForm.reset()}
                    >
                      Reset
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Grid>

        {/* Job Application Form */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                Job Application Form
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Pre-filled form with validation and custom error messages
              </Typography>
              
              <form onSubmit={jobApplicationForm.handleSubmit(onJobApplicationSubmit)}>
                <Stack spacing={3}>
                  <FormSingleSelect
                    name="position"
                    control={jobApplicationForm.control}
                    label="Position *"
                    placeholder="Select position"
                    options={positions}
                    helperText="Choose the position you're applying for"
                  />

                  <FormSingleSelect
                    name="location"
                    control={jobApplicationForm.control}
                    label="Preferred Location *"
                    placeholder="Select location"
                    options={countries}
                    clearable
                  />

                  <FormMultiSelect
                    name="skills"
                    control={jobApplicationForm.control}
                    label="Required Skills *"
                    placeholder="Select skills"
                    options={skills}
                    showSelectAll
                    selectAllText="Select All Skills"
                    helperText="Minimum 3 skills required"
                  />

                  <FormSingleSelect
                    name="experience"
                    control={jobApplicationForm.control}
                    label="Experience Level *"
                    placeholder="Select experience"
                    options={experienceLevels}
                    size="small"
                    helperText="Pre-selected to Mid-level"
                  />

                  <FormSingleSelect
                    name="availability"
                    control={jobApplicationForm.control}
                    label="Availability *"
                    placeholder="When can you start?"
                    options={availability}
                  />

                  <Divider />

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={jobApplicationForm.formState.isSubmitting}
                    >
                      Submit Application
                    </Button>
                    <Button
                      type="button"
                      variant="outlined"
                      onClick={() => jobApplicationForm.reset()}
                    >
                      Reset
                    </Button>
                  </Box>
                </Stack>
              </form>
            </CardContent>
          </Card>
        </Grid>

        {/* Form State Display */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                Form State (Development)
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    User Profile Form State
                  </Typography>
                  <Box
                    component="pre"
                    sx={{
                      backgroundColor: 'grey.100',
                      p: 2,
                      borderRadius: 1,
                      fontSize: '0.75rem',
                      overflow: 'auto',
                      maxHeight: 300,
                    }}
                  >
                    {JSON.stringify({
                      values: userProfileForm.watch(),
                      errors: userProfileForm.formState.errors,
                      isValid: userProfileForm.formState.isValid,
                      isDirty: userProfileForm.formState.isDirty,
                    }, null, 2)}
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Job Application Form State
                  </Typography>
                  <Box
                    component="pre"
                    sx={{
                      backgroundColor: 'grey.100',
                      p: 2,
                      borderRadius: 1,
                      fontSize: '0.75rem',
                      overflow: 'auto',
                      maxHeight: 300,
                    }}
                  >
                    {JSON.stringify({
                      values: jobApplicationForm.watch(),
                      errors: jobApplicationForm.formState.errors,
                      isValid: jobApplicationForm.formState.isValid,
                      isDirty: jobApplicationForm.formState.isDirty,
                    }, null, 2)}
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default FormSelectExamples;