'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Alert,
  AlertTitle,
  Divider,
  Paper,
} from '@mui/material';
import { LexicalContentEditor } from '@/components/ui/LexicalContentEditor';

export const LexicalEditorDemo: React.FC = () => {
  const [productOverview, setProductOverview] = useState(`
    <h2>Premium Wireless Headphones</h2>
    <p>Experience <strong>crystal-clear audio</strong> with our latest wireless headphones featuring advanced noise cancellation technology.</p>
    <blockquote>Designed for audiophiles who demand the best sound quality and comfort.</blockquote>
  `);

  const [productFeatures, setProductFeatures] = useState(`
    <h3>Key Features</h3>
    <ul>
      <li><strong>Active Noise Cancellation</strong> - Block out unwanted noise</li>
      <li><strong>30-hour Battery Life</strong> - All-day listening</li>
      <li><strong>Quick Charge</strong> - 5 minutes for 2 hours of playback</li>
      <li><strong>Premium Materials</strong> - Soft leather and aluminum construction</li>
    </ul>
    <p><em>Perfect for travel, work, or relaxation.</em></p>
  `);

  const [productSpecifications, setProductSpecifications] = useState(`
    <h3>Technical Specifications</h3>
    <p><strong>Audio:</strong></p>
    <ul>
      <li>Driver Size: 40mm</li>
      <li>Frequency Response: 20Hz - 20kHz</li>
      <li>Impedance: 32 ohms</li>
    </ul>
    
    <p><strong>Connectivity:</strong></p>
    <ul>
      <li>Bluetooth 5.0</li>
      <li>3.5mm audio jack</li>
      <li>USB-C charging</li>
    </ul>
    
    <pre><code>Dimensions: 190 x 160 x 80mm
Weight: 250g
Warranty: 2 years</code></pre>
  `);

  return (
    <Box sx={{ p: 3, maxWidth: 1400, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, mb: 4 }}>
        Lexical Content Editor Demo
      </Typography>
      
      <Alert severity="info" sx={{ mb: 4 }}>
        <AlertTitle>Enhanced Content Editing</AlertTitle>
        The Lexical Content Editor provides rich text editing capabilities for product content.
        It supports headings, formatting, lists, quotes, code blocks, and more.
      </Alert>

      <Grid container spacing={4}>
        {/* Product Overview */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                Product Overview Editor
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Create compelling product overviews with rich formatting options
              </Typography>
              
              <LexicalContentEditor
                label="Product Overview"
                value={productOverview}
                onChange={setProductOverview}
                placeholder="Describe what makes your product unique, its main purpose, and why customers should choose it..."
                height={300}
                helperText="Write a compelling overview that highlights your product's value proposition"
                required
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Product Features */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                Product Features Editor
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                List key features and benefits with structured formatting
              </Typography>
              
              <LexicalContentEditor
                label="Product Features"
                value={productFeatures}
                onChange={setProductFeatures}
                placeholder="List the key features and benefits of your product. Use bullet points or numbered lists for better readability..."
                height={300}
                helperText="Highlight the most important features that set your product apart"
                required
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Product Specifications */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                Product Specifications Editor
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Provide detailed technical information with code blocks and structured data
              </Typography>
              
              <LexicalContentEditor
                label="Product Specifications"
                value={productSpecifications}
                onChange={setProductSpecifications}
                placeholder="Include technical specifications, dimensions, materials, compatibility, warranty information, etc..."
                height={300}
                helperText="Provide detailed technical information that customers need to make informed decisions"
                required
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Preview Section */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                Content Preview
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                See how your content will appear to customers
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Paper variant="outlined" sx={{ p: 2, height: 300, overflow: 'auto' }}>
                    <Typography variant="h6" gutterBottom color="primary">
                      Overview
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                    <Box 
                      dangerouslySetInnerHTML={{ __html: productOverview }}
                      sx={{
                        '& h1, & h2, & h3': { color: 'text.primary', mt: 1, mb: 1 },
                        '& p': { mb: 1 },
                        '& blockquote': { 
                          borderLeft: '4px solid #ccc', 
                          pl: 2, 
                          fontStyle: 'italic',
                          color: 'text.secondary'
                        }
                      }}
                    />
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper variant="outlined" sx={{ p: 2, height: 300, overflow: 'auto' }}>
                    <Typography variant="h6" gutterBottom color="primary">
                      Features
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                    <Box 
                      dangerouslySetInnerHTML={{ __html: productFeatures }}
                      sx={{
                        '& h1, & h2, & h3': { color: 'text.primary', mt: 1, mb: 1 },
                        '& p': { mb: 1 },
                        '& ul, & ol': { pl: 2 },
                        '& li': { mb: 0.5 }
                      }}
                    />
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper variant="outlined" sx={{ p: 2, height: 300, overflow: 'auto' }}>
                    <Typography variant="h6" gutterBottom color="primary">
                      Specifications
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                    <Box 
                      dangerouslySetInnerHTML={{ __html: productSpecifications }}
                      sx={{
                        '& h1, & h2, & h3': { color: 'text.primary', mt: 1, mb: 1 },
                        '& p': { mb: 1 },
                        '& ul, & ol': { pl: 2 },
                        '& li': { mb: 0.5 },
                        '& pre': { 
                          backgroundColor: '#f5f5f5', 
                          p: 1, 
                          borderRadius: 1,
                          fontSize: '0.875rem'
                        }
                      }}
                    />
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Features Overview */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                Editor Features
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Text Formatting
                  </Typography>
                  <ul>
                    <li><strong>Bold</strong>, <em>Italic</em>, and <u>Underline</u> text</li>
                    <li>Multiple heading levels (H1, H2, H3)</li>
                    <li>Text alignment (left, center, right)</li>
                    <li>Paragraph formatting</li>
                  </ul>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Content Structure
                  </Typography>
                  <ul>
                    <li>Bullet and numbered lists</li>
                    <li>Blockquotes for emphasis</li>
                    <li>Code blocks for technical specs</li>
                    <li>Undo/Redo functionality</li>
                  </ul>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default LexicalEditorDemo;