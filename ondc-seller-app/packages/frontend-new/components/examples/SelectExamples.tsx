'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Divider,
  Stack,
  Button,
  Alert,
} from '@mui/material';
import { Select, SelectOption } from '../ui/Select';
import { MultiSelect } from '../ui/MultiSelect';
import { SingleSelect } from '../ui/SingleSelect';

// Sample data
const countries: SelectOption[] = [
  { value: 'us', label: 'United States', description: 'North America' },
  { value: 'ca', label: 'Canada', description: 'North America' },
  { value: 'uk', label: 'United Kingdom', description: 'Europe' },
  { value: 'de', label: 'Germany', description: 'Europe' },
  { value: 'fr', label: 'France', description: 'Europe' },
  { value: 'jp', label: 'Japan', description: 'Asia' },
  { value: 'au', label: 'Australia', description: 'Oceania' },
  { value: 'br', label: 'Brazil', description: 'South America' },
  { value: 'in', label: 'India', description: 'Asia' },
  { value: 'mx', label: 'Mexico', description: 'North America', disabled: true },
];

const categories: SelectOption[] = [
  { value: 'electronics', label: 'Electronics' },
  { value: 'clothing', label: 'Clothing & Fashion' },
  { value: 'books', label: 'Books & Media' },
  { value: 'home', label: 'Home & Garden' },
  { value: 'sports', label: 'Sports & Outdoors' },
  { value: 'toys', label: 'Toys & Games' },
  { value: 'automotive', label: 'Automotive' },
  { value: 'health', label: 'Health & Beauty' },
];

const priorities: SelectOption[] = [
  { value: 'low', label: 'Low Priority', description: 'Can be addressed later' },
  { value: 'medium', label: 'Medium Priority', description: 'Should be addressed soon' },
  { value: 'high', label: 'High Priority', description: 'Needs immediate attention' },
  { value: 'urgent', label: 'Urgent', description: 'Critical issue' },
];

export const SelectExamples: React.FC = () => {
  // Single Select states
  const [selectedCountry, setSelectedCountry] = useState<string | number>('');
  const [selectedPriority, setSelectedPriority] = useState<string | number>('medium');
  const [clearableValue, setClearableValue] = useState<string | number>('us');

  // Multi Select states
  const [selectedCategories, setSelectedCategories] = useState<string[] | number[]>(['electronics', 'books']);
  const [selectedCountries, setSelectedCountries] = useState<string[] | number[]>([]);
  const [limitedSelection, setLimitedSelection] = useState<string[] | number[]>(['low', 'high']);

  // Form validation states
  const [formData, setFormData] = useState({
    country: '',
    categories: [] as string[],
    priority: '',
  });
  const [errors, setErrors] = useState({
    country: false,
    categories: false,
    priority: false,
  });

  const handleFormSubmit = () => {
    const newErrors = {
      country: !formData.country,
      categories: formData.categories.length === 0,
      priority: !formData.priority,
    };
    
    setErrors(newErrors);
    
    if (!Object.values(newErrors).some(Boolean)) {
      alert('Form submitted successfully!');
    }
  };

  const resetForm = () => {
    setFormData({ country: '', categories: [], priority: '' });
    setErrors({ country: false, categories: false, priority: false });
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom sx={{ mb: 4, fontWeight: 600 }}>
        Select Components Examples
      </Typography>
      
      <Alert severity="info" sx={{ mb: 4 }}>
        These are reusable Material Design 3 select components that can be used throughout your application.
        They support single selection, multiple selection, validation, and various customization options.
      </Alert>

      <Grid container spacing={4}>
        {/* Single Select Examples */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: 'fit-content' }}>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                Single Select
              </Typography>
              
              <Stack spacing={3}>
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Basic Single Select
                  </Typography>
                  <SingleSelect
                    label="Country"
                    placeholder="Choose a country"
                    value={selectedCountry}
                    onChange={(value) => setSelectedCountry(value || '')}
                    options={countries}
                    helperText="Select your country of residence"
                  />
                  <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
                    Selected: {selectedCountry || 'None'}
                  </Typography>
                </Box>

                <Divider />

                <Box>
                  <Typography variant="h6" gutterBottom>
                    Pre-selected with Description
                  </Typography>
                  <SingleSelect
                    label="Priority Level"
                    value={selectedPriority}
                    onChange={(value) => setSelectedPriority(value || '')}
                    options={priorities}
                    size="small"
                    helperText="Current priority level"
                  />
                </Box>

                <Divider />

                <Box>
                  <Typography variant="h6" gutterBottom>
                    Clearable Single Select
                  </Typography>
                  <SingleSelect
                    label="Country (Clearable)"
                    value={clearableValue}
                    onChange={(value) => setClearableValue(value || '')}
                    options={countries}
                    clearable
                    helperText="You can clear this selection"
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Multi Select Examples */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: 'fit-content' }}>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                Multi Select
              </Typography>
              
              <Stack spacing={3}>
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Basic Multi Select
                  </Typography>
                  <MultiSelect
                    label="Categories"
                    placeholder="Select categories"
                    value={selectedCategories}
                    onChange={setSelectedCategories}
                    options={categories}
                    helperText="Choose one or more categories"
                  />
                  <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
                    Selected: {selectedCategories.length} items
                  </Typography>
                </Box>

                <Divider />

                <Box>
                  <Typography variant="h6" gutterBottom>
                    Multi Select with Select All
                  </Typography>
                  <MultiSelect
                    label="Countries"
                    value={selectedCountries}
                    onChange={setSelectedCountries}
                    options={countries}
                    showSelectAll
                    selectAllText="Select All Countries"
                    helperText="Use 'Select All' to toggle all options"
                    size="small"
                  />
                </Box>

                <Divider />

                <Box>
                  <Typography variant="h6" gutterBottom>
                    Limited Display (Max 2 Tags)
                  </Typography>
                  <MultiSelect
                    label="Priorities"
                    value={limitedSelection}
                    onChange={setLimitedSelection}
                    options={priorities}
                    maxTags={2}
                    chipVariant="outlined"
                    chipColor="secondary"
                    helperText="Shows max 2 tags, then '+X more'"
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Form Validation Example */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                Form Validation Example
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <SingleSelect
                    label="Country *"
                    placeholder="Select country"
                    value={formData.country}
                    onChange={(value) => {
                      setFormData(prev => ({ ...prev, country: value as string || '' }));
                      setErrors(prev => ({ ...prev, country: false }));
                    }}
                    options={countries}
                    error={errors.country}
                    helperText={errors.country ? 'Country is required' : 'Select your country'}
                    required
                  />
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <MultiSelect
                    label="Categories *"
                    placeholder="Select categories"
                    value={formData.categories}
                    onChange={(value) => {
                      setFormData(prev => ({ ...prev, categories: value as string[] }));
                      setErrors(prev => ({ ...prev, categories: false }));
                    }}
                    options={categories}
                    error={errors.categories}
                    helperText={errors.categories ? 'At least one category is required' : 'Select relevant categories'}
                    required
                  />
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <SingleSelect
                    label="Priority *"
                    placeholder="Select priority"
                    value={formData.priority}
                    onChange={(value) => {
                      setFormData(prev => ({ ...prev, priority: value as string || '' }));
                      setErrors(prev => ({ ...prev, priority: false }));
                    }}
                    options={priorities}
                    error={errors.priority}
                    helperText={errors.priority ? 'Priority is required' : 'Set the priority level'}
                    required
                  />
                </Grid>
              </Grid>
              
              <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                <Button variant="contained" onClick={handleFormSubmit}>
                  Submit Form
                </Button>
                <Button variant="outlined" onClick={resetForm}>
                  Reset
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Size Variations */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                Size Variations
              </Typography>
              
              <Stack spacing={3}>
                <SingleSelect
                  label="Small Size"
                  value=""
                  onChange={() => {}}
                  options={categories.slice(0, 4)}
                  size="small"
                  placeholder="Small select"
                />
                
                <SingleSelect
                  label="Medium Size (Default)"
                  value=""
                  onChange={() => {}}
                  options={categories.slice(0, 4)}
                  size="medium"
                  placeholder="Medium select"
                />
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Disabled States */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                Disabled States
              </Typography>
              
              <Stack spacing={3}>
                <SingleSelect
                  label="Disabled Single Select"
                  value="electronics"
                  onChange={() => {}}
                  options={categories}
                  disabled
                  helperText="This select is disabled"
                />
                
                <MultiSelect
                  label="Disabled Multi Select"
                  value={['electronics', 'books']}
                  onChange={() => {}}
                  options={categories}
                  disabled
                  helperText="This multi-select is disabled"
                />
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SelectExamples;