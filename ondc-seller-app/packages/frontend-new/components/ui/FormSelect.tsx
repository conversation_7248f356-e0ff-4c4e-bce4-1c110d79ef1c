'use client';

import React, { forwardRef } from 'react';
import { Controller, Control, FieldPath, FieldValues } from 'react-hook-form';
import { Select, SelectProps } from './Select';
import { MultiSelect, MultiSelectProps } from './MultiSelect';
import { SingleSelect, SingleSelectProps } from './SingleSelect';

// Generic form select props
interface BaseFormSelectProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>;
  control: Control<TFieldValues>;
  rules?: object;
  defaultValue?: any;
}

// Form Single Select
export interface FormSingleSelectProps<TFieldValues extends FieldValues>
  extends BaseFormSelectProps<TFieldValues>,
    Omit<SingleSelectProps, 'value' | 'onChange' | 'name'> {}

export const FormSingleSelect = <TFieldValues extends FieldValues>({
  name,
  control,
  rules,
  defaultValue = '',
  ...selectProps
}: FormSingleSelectProps<TFieldValues>) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      defaultValue={defaultValue}
      render={({ field, fieldState }) => (
        <SingleSelect
          {...selectProps}
          {...field}
          value={field.value || ''}
          onChange={(value) => field.onChange(value)}
          error={!!fieldState.error}
          helperText={fieldState.error?.message || selectProps.helperText}
        />
      )}
    />
  );
};

// Form Multi Select
export interface FormMultiSelectProps<TFieldValues extends FieldValues>
  extends BaseFormSelectProps<TFieldValues>,
    Omit<MultiSelectProps, 'value' | 'onChange' | 'name'> {}

export const FormMultiSelect = <TFieldValues extends FieldValues>({
  name,
  control,
  rules,
  defaultValue = [],
  ...selectProps
}: FormMultiSelectProps<TFieldValues>) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      defaultValue={defaultValue}
      render={({ field, fieldState }) => (
        <MultiSelect
          {...selectProps}
          {...field}
          value={field.value || []}
          onChange={(value) => field.onChange(value)}
          error={!!fieldState.error}
          helperText={fieldState.error?.message || selectProps.helperText}
        />
      )}
    />
  );
};

// Form Base Select (can be single or multi)
export interface FormSelectProps<TFieldValues extends FieldValues>
  extends BaseFormSelectProps<TFieldValues>,
    Omit<SelectProps, 'value' | 'onChange' | 'name'> {}

export const FormSelect = <TFieldValues extends FieldValues>({
  name,
  control,
  rules,
  defaultValue,
  multiple = false,
  ...selectProps
}: FormSelectProps<TFieldValues>) => {
  const defaultVal = defaultValue !== undefined ? defaultValue : (multiple ? [] : '');
  
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      defaultValue={defaultVal}
      render={({ field, fieldState }) => (
        <Select
          {...selectProps}
          {...field}
          multiple={multiple}
          value={field.value || defaultVal}
          onChange={(value) => field.onChange(value)}
          error={!!fieldState.error}
          helperText={fieldState.error?.message || selectProps.helperText}
        />
      )}
    />
  );
};

export default FormSelect;