'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  Toolbar,
  IconButton,
  Divider,
  FormHelperText,
  ButtonGroup,
  Tooltip,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  FormatBold,
  FormatItalic,
  FormatUnderlined,
  FormatListBulleted,
  FormatListNumbered,
  Undo,
  Redo,
  Title,
  Subject,
  FormatQuote,
  Code,
  Link,
  FormatAlignLeft,
  FormatAlignCenter,
  FormatAlignRight,
  FormatColorText,
  FormatSize,
} from '@mui/icons-material';

interface LexicalContentEditorProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  error?: string;
  helperText?: string;
  placeholder?: string;
  height?: number;
  required?: boolean;
}

export const LexicalContentEditor: React.FC<LexicalContentEditorProps> = ({
  value,
  onChange,
  label,
  error,
  helperText,
  placeholder = 'Start writing your content here...',
  height = 300,
  required = false,
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isEditorFocused, setIsEditorFocused] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState('paragraph');

  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);

  const handleInput = () => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML;
      onChange(content);
    }
  };

  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      editorRef.current.focus();
      handleInput();
    }
  };

  const insertHeading = (level: number) => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const heading = document.createElement(`h${level}`);
      heading.textContent = 'Heading';
      
      try {
        range.deleteContents();
        range.insertNode(heading);
        
        const newRange = document.createRange();
        newRange.setStartAfter(heading);
        newRange.collapse(true);
        selection.removeAllRanges();
        selection.addRange(newRange);
        
        setSelectedFormat(`h${level}`);
        handleInput();
      } catch (e) {
        console.warn('Could not insert heading:', e);
      }
    }
  };

  const insertList = (type: 'ul' | 'ol') => {
    execCommand(type === 'ul' ? 'insertUnorderedList' : 'insertOrderedList');
  };

  const formatText = (command: string) => {
    execCommand(command);
  };

  const insertBlockquote = () => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const blockquote = document.createElement('blockquote');
      blockquote.textContent = 'Quote text';
      blockquote.style.borderLeft = '4px solid #ccc';
      blockquote.style.paddingLeft = '16px';
      blockquote.style.margin = '16px 0';
      blockquote.style.fontStyle = 'italic';
      
      try {
        range.deleteContents();
        range.insertNode(blockquote);
        
        const newRange = document.createRange();
        newRange.setStartAfter(blockquote);
        newRange.collapse(true);
        selection.removeAllRanges();
        selection.addRange(newRange);
        
        handleInput();
      } catch (e) {
        console.warn('Could not insert blockquote:', e);
      }
    }
  };

  const insertCodeBlock = () => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const pre = document.createElement('pre');
      const code = document.createElement('code');
      code.textContent = 'Code block';
      pre.appendChild(code);
      pre.style.backgroundColor = '#f5f5f5';
      pre.style.padding = '12px';
      pre.style.borderRadius = '4px';
      pre.style.margin = '16px 0';
      pre.style.fontFamily = 'monospace';
      
      try {
        range.deleteContents();
        range.insertNode(pre);
        
        const newRange = document.createRange();
        newRange.setStartAfter(pre);
        newRange.collapse(true);
        selection.removeAllRanges();
        selection.addRange(newRange);
        
        handleInput();
      } catch (e) {
        console.warn('Could not insert code block:', e);
      }
    }
  };

  const handleFormatChange = (format: string) => {
    setSelectedFormat(format);
    switch (format) {
      case 'h1':
        insertHeading(1);
        break;
      case 'h2':
        insertHeading(2);
        break;
      case 'h3':
        insertHeading(3);
        break;
      case 'blockquote':
        insertBlockquote();
        break;
      case 'code':
        insertCodeBlock();
        break;
      default:
        execCommand('formatBlock', format);
        break;
    }
  };

  const showPlaceholder = !value || value.trim() === '';

  return (
    <Box>
      {label && (
        <Typography 
          variant="body2" 
          component="label" 
          sx={{ 
            display: 'block', 
            mb: 1, 
            fontWeight: 500,
            color: error ? 'error.main' : 'text.primary'
          }}
        >
          {label}
          {required && <span style={{ color: 'red', marginLeft: '4px' }}>*</span>}
        </Typography>
      )}
      
      <Paper
        variant="outlined"
        sx={{
          borderColor: error ? 'error.main' : 'divider',
          borderRadius: 1,
          overflow: 'hidden'
        }}
      >
        <Toolbar
          sx={{
            borderBottom: '1px solid',
            borderColor: 'divider',
            p: 1,
            gap: 1,
            flexWrap: 'wrap',
            minHeight: 'auto',
            backgroundColor: 'grey.50'
          }}
        >
          {/* Format Selector */}
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <Select
              value={selectedFormat}
              onChange={(e) => handleFormatChange(e.target.value)}
              displayEmpty
              sx={{ fontSize: '0.875rem' }}
            >
              <MenuItem value="paragraph">Paragraph</MenuItem>
              <MenuItem value="h1">Heading 1</MenuItem>
              <MenuItem value="h2">Heading 2</MenuItem>
              <MenuItem value="h3">Heading 3</MenuItem>
              <MenuItem value="blockquote">Quote</MenuItem>
              <MenuItem value="code">Code Block</MenuItem>
            </Select>
          </FormControl>

          <Divider orientation="vertical" flexItem />

          {/* Text Formatting */}
          <ButtonGroup size="small">
            <Tooltip title="Bold">
              <IconButton
                onClick={() => formatText('bold')}
                size="small"
              >
                <FormatBold />
              </IconButton>
            </Tooltip>
            <Tooltip title="Italic">
              <IconButton
                onClick={() => formatText('italic')}
                size="small"
              >
                <FormatItalic />
              </IconButton>
            </Tooltip>
            <Tooltip title="Underline">
              <IconButton
                onClick={() => formatText('underline')}
                size="small"
              >
                <FormatUnderlined />
              </IconButton>
            </Tooltip>
          </ButtonGroup>

          <Divider orientation="vertical" flexItem />

          {/* Lists */}
          <ButtonGroup size="small">
            <Tooltip title="Bullet List">
              <IconButton
                onClick={() => insertList('ul')}
                size="small"
              >
                <FormatListBulleted />
              </IconButton>
            </Tooltip>
            <Tooltip title="Numbered List">
              <IconButton
                onClick={() => insertList('ol')}
                size="small"
              >
                <FormatListNumbered />
              </IconButton>
            </Tooltip>
          </ButtonGroup>

          <Divider orientation="vertical" flexItem />

          {/* Alignment */}
          <ButtonGroup size="small">
            <Tooltip title="Align Left">
              <IconButton
                onClick={() => execCommand('justifyLeft')}
                size="small"
              >
                <FormatAlignLeft />
              </IconButton>
            </Tooltip>
            <Tooltip title="Align Center">
              <IconButton
                onClick={() => execCommand('justifyCenter')}
                size="small"
              >
                <FormatAlignCenter />
              </IconButton>
            </Tooltip>
            <Tooltip title="Align Right">
              <IconButton
                onClick={() => execCommand('justifyRight')}
                size="small"
              >
                <FormatAlignRight />
              </IconButton>
            </Tooltip>
          </ButtonGroup>

          <Divider orientation="vertical" flexItem />

          {/* Special Elements */}
          <ButtonGroup size="small">
            <Tooltip title="Quote">
              <IconButton
                onClick={insertBlockquote}
                size="small"
              >
                <FormatQuote />
              </IconButton>
            </Tooltip>
            <Tooltip title="Code Block">
              <IconButton
                onClick={insertCodeBlock}
                size="small"
              >
                <Code />
              </IconButton>
            </Tooltip>
          </ButtonGroup>

          <Divider orientation="vertical" flexItem />

          {/* Undo/Redo */}
          <ButtonGroup size="small">
            <Tooltip title="Undo">
              <IconButton
                onClick={() => execCommand('undo')}
                size="small"
              >
                <Undo />
              </IconButton>
            </Tooltip>
            <Tooltip title="Redo">
              <IconButton
                onClick={() => execCommand('redo')}
                size="small"
              >
                <Redo />
              </IconButton>
            </Tooltip>
          </ButtonGroup>
        </Toolbar>

        <Box sx={{ position: 'relative' }}>
          {showPlaceholder && (
            <Box
              sx={{
                position: 'absolute',
                top: 16,
                left: 16,
                color: 'text.secondary',
                fontStyle: 'italic',
                pointerEvents: 'none',
                fontSize: '14px',
                zIndex: 1
              }}
            >
              {placeholder}
            </Box>
          )}
          
          <Box
            ref={editorRef}
            contentEditable
            suppressContentEditableWarning
            onInput={handleInput}
            onFocus={() => setIsEditorFocused(true)}
            onBlur={() => setIsEditorFocused(false)}
            sx={{
              minHeight: `${height}px`,
              padding: '16px',
              outline: 'none',
              fontSize: '14px',
              lineHeight: '1.6',
              fontFamily: 'inherit',
              position: 'relative',
              zIndex: 2,
              '& h1': {
                fontSize: '2rem',
                fontWeight: 'bold',
                margin: '16px 0 8px 0',
                lineHeight: 1.2,
                color: 'text.primary'
              },
              '& h2': {
                fontSize: '1.5rem',
                fontWeight: 'bold',
                margin: '14px 0 6px 0',
                lineHeight: 1.3,
                color: 'text.primary'
              },
              '& h3': {
                fontSize: '1.25rem',
                fontWeight: 'bold',
                margin: '12px 0 4px 0',
                lineHeight: 1.4,
                color: 'text.primary'
              },
              '& p': {
                margin: '8px 0',
                lineHeight: 1.6
              },
              '& ul, & ol': {
                margin: '8px 0',
                paddingLeft: '24px'
              },
              '& li': {
                margin: '4px 0',
                lineHeight: 1.5
              },
              '& blockquote': {
                borderLeft: '4px solid #ccc',
                paddingLeft: '16px',
                margin: '16px 0',
                fontStyle: 'italic',
                color: 'text.secondary'
              },
              '& pre': {
                backgroundColor: '#f5f5f5',
                padding: '12px',
                borderRadius: '4px',
                margin: '16px 0',
                fontFamily: 'monospace',
                fontSize: '13px',
                overflow: 'auto'
              },
              '& code': {
                backgroundColor: '#f5f5f5',
                padding: '2px 4px',
                borderRadius: '3px',
                fontFamily: 'monospace',
                fontSize: '13px'
              },
              '& strong': {
                fontWeight: 'bold'
              },
              '& em': {
                fontStyle: 'italic'
              },
              '& u': {
                textDecoration: 'underline'
              }
            }}
          />
        </Box>
      </Paper>
      
      {(error || helperText) && (
        <FormHelperText error={!!error} sx={{ mt: 1 }}>
          {error || helperText}
        </FormHelperText>
      )}
      
      <Box sx={{ mt: 1 }}>
        <FormHelperText>
          💡 Use the toolbar above to format your content with headings, lists, quotes, and more
        </FormHelperText>
      </Box>
    </Box>
  );
};

export default LexicalContentEditor;