'use client';

import React, { forwardRef } from 'react';
import { Select, SelectProps, SelectOption } from './Select';
import { IconButton, InputAdornment } from '@mui/material';
import { Clear as ClearIcon } from '@mui/icons-material';

export interface SingleSelectProps extends Omit<SelectProps, 'multiple' | 'value' | 'onChange'> {
  value?: string | number;
  onChange?: (value: string | number | null) => void;
  clearable?: boolean;
  clearIcon?: React.ReactNode;
  onClear?: () => void;
}

export const SingleSelect = forwardRef<HTMLDivElement, SingleSelectProps>(({
  value = '',
  onChange,
  clearable = false,
  clearIcon,
  onClear,
  placeholder = 'Select an option...',
  ...props
}, ref) => {
  const handleChange = (newValue: string | number | string[] | number[]) => {
    // For single select, we expect a single value
    const singleValue = Array.isArray(newValue) ? newValue[0] : newValue;
    onChange?.(singleValue || null);
  };

  const handleClear = (event: React.MouseEvent) => {
    event.stopPropagation();
    onChange?.(null);
    onClear?.();
  };

  const showClearButton = clearable && value !== '' && value != null;

  return (
    <Select
      ref={ref}
      multiple={false}
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      sx={{
        '& .MuiSelect-select': {
          paddingRight: showClearButton ? '48px !important' : undefined,
        },
        ...props.sx,
      }}
      {...props}
      // Add clear button as end adornment
      endAdornment={
        showClearButton ? (
          <InputAdornment position="end" sx={{ position: 'absolute', right: 32 }}>
            <IconButton
              size="small"
              onClick={handleClear}
              sx={{
                padding: '4px',
                color: 'text.secondary',
                '&:hover': {
                  color: 'text.primary',
                  backgroundColor: 'action.hover',
                },
              }}
            >
              {clearIcon || <ClearIcon fontSize="small" />}
            </IconButton>
          </InputAdornment>
        ) : undefined
      }
    />
  );
});

SingleSelect.displayName = 'SingleSelect';

export default SingleSelect;