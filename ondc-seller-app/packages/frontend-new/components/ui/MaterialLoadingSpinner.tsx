'use client';

import React from 'react';
import { Box, CircularProgress, Typography, Backdrop } from '@mui/material';

interface MaterialLoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  text?: string;
  fullScreen?: boolean;
  backdrop?: boolean;
}

export const MaterialLoadingSpinner: React.FC<MaterialLoadingSpinnerProps> = ({ 
  size = 'medium', 
  text = 'Loading...', 
  fullScreen = false,
  backdrop = false
}) => {
  const getSize = () => {
    switch (size) {
      case 'small':
        return 24;
      case 'medium':
        return 40;
      case 'large':
        return 56;
      default:
        return 40;
    }
  };

  const content = (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 2,
        p: 3,
      }}
    >
      <CircularProgress 
        size={getSize()} 
        thickness={4}
        sx={{
          color: 'primary.main',
        }}
      />
      {text && (
        <Typography 
          variant="body2" 
          color="text.secondary"
          sx={{ 
            textAlign: 'center',
            fontSize: size === 'small' ? '0.75rem' : '0.875rem'
          }}
        >
          {text}
        </Typography>
      )}
    </Box>
  );

  if (backdrop) {
    return (
      <Backdrop
        open={true}
        sx={{
          color: '#fff',
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 2,
            p: 4,
            backgroundColor: 'background.paper',
            borderRadius: 2,
            boxShadow: 3,
            minWidth: 200,
          }}
        >
          <CircularProgress 
            size={getSize()} 
            thickness={4}
            sx={{
              color: 'primary.main',
            }}
          />
          {text && (
            <Typography 
              variant="body1" 
              color="text.primary"
              sx={{ 
                textAlign: 'center',
                fontWeight: 500,
              }}
            >
              {text}
            </Typography>
          )}
        </Box>
      </Backdrop>
    );
  }

  if (fullScreen) {
    return (
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          zIndex: 9999,
        }}
      >
        {content}
      </Box>
    );
  }

  return content;
};

export default MaterialLoadingSpinner;