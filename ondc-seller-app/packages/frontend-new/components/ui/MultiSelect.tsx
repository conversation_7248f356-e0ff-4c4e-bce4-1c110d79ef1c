'use client';

import React, { forwardRef } from 'react';
import { Select, SelectProps, SelectOption } from './Select';
import { Chip, Box } from '@mui/material';

export interface MultiSelectProps extends Omit<SelectProps, 'multiple' | 'value' | 'onChange'> {
  value?: string[] | number[];
  onChange?: (value: string[] | number[]) => void;
  maxTags?: number;
  showSelectAll?: boolean;
  selectAllText?: string;
  chipVariant?: 'filled' | 'outlined';
  chipColor?: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
}

export const MultiSelect = forwardRef<HTMLDivElement, MultiSelectProps>(({
  value = [],
  onChange,
  options = [],
  maxTags,
  showSelectAll = false,
  selectAllText = 'Select All',
  chipVariant = 'filled',
  chipColor = 'primary',
  renderValue,
  placeholder = 'Select options...',
  ...props
}, ref) => {
  const handleChange = (newValue: string | number | string[] | number[]) => {
    if (Array.isArray(newValue)) {
      // Handle "Select All" functionality
      if (showSelectAll && newValue.includes('__select_all__')) {
        const allValues = options.filter(opt => !opt.disabled).map(opt => opt.value);
        const isAllSelected = allValues.every(val => value.includes(val));
        
        if (isAllSelected) {
          // Deselect all
          onChange?.([]);
        } else {
          // Select all
          onChange?.(allValues as string[] | number[]);
        }
        return;
      }
      
      // Filter out the select all option from the actual values
      const filteredValues = newValue.filter(val => val !== '__select_all__');
      onChange?.(filteredValues as string[] | number[]);
    }
  };

  const customRenderValue = (selected: string | number | string[] | number[]) => {
    if (!Array.isArray(selected) || selected.length === 0) {
      return <em style={{ color: 'rgba(0, 0, 0, 0.6)' }}>{placeholder}</em>;
    }

    // If maxTags is set and we exceed it, show count
    if (maxTags && selected.length > maxTags) {
      const visibleTags = selected.slice(0, maxTags);
      const remainingCount = selected.length - maxTags;
      
      return (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, alignItems: 'center' }}>
          {visibleTags.map((val) => {
            const option = options.find(opt => opt.value === val);
            return (
              <Chip
                key={val}
                label={option ? option.label : val}
                size="small"
                variant={chipVariant}
                color={chipColor}
                sx={{
                  height: 24,
                  fontSize: '0.75rem',
                  maxWidth: 120,
                  '& .MuiChip-label': {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  },
                }}
              />
            );
          })}
          <Chip
            label={`+${remainingCount} more`}
            size="small"
            variant="outlined"
            sx={{
              height: 24,
              fontSize: '0.75rem',
              color: 'text.secondary',
              borderColor: 'text.secondary',
            }}
          />
        </Box>
      );
    }

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
        {selected.map((val) => {
          const option = options.find(opt => opt.value === val);
          return (
            <Chip
              key={val}
              label={option ? option.label : val}
              size="small"
              variant={chipVariant}
              color={chipColor}
              sx={{
                height: 24,
                fontSize: '0.75rem',
                maxWidth: 120,
                '& .MuiChip-label': {
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                },
              }}
            />
          );
        })}
      </Box>
    );
  };

  // Add "Select All" option if enabled
  const enhancedOptions: SelectOption[] = showSelectAll ? [
    {
      value: '__select_all__',
      label: selectAllText,
      description: 'Toggle all options',
    },
    ...options,
  ] : options;

  // Check if all options are selected (for Select All checkbox state)
  const allSelectableValues = options.filter(opt => !opt.disabled).map(opt => opt.value);
  const isAllSelected = allSelectableValues.length > 0 && 
    allSelectableValues.every(val => value.includes(val));

  // Modify the value to include select all state
  const enhancedValue = showSelectAll && isAllSelected 
    ? [...value, '__select_all__'] 
    : value;

  return (
    <Select
      ref={ref}
      multiple
      value={enhancedValue}
      onChange={handleChange}
      options={enhancedOptions}
      renderValue={renderValue || customRenderValue}
      placeholder={placeholder}
      {...props}
    />
  );
});

MultiSelect.displayName = 'MultiSelect';

export default MultiSelect;