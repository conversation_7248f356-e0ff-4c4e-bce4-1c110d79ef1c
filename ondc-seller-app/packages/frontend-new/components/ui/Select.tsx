'use client';

import React, { forwardRef } from 'react';
import {
  FormControl,
  InputLabel,
  Select as MuiSelect,
  MenuItem,
  FormHelperText,
  Chip,
  Box,
  SelectChangeEvent,
  OutlinedInput,
  ListItemText,
  Checkbox,
} from '@mui/material';
import { SxProps, Theme } from '@mui/material/styles';

export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  description?: string;
}

export interface SelectProps {
  // Basic props
  label?: string;
  placeholder?: string;
  value?: string | number | string[] | number[];
  options: SelectOption[];
  onChange?: (value: string | number | string[] | number[]) => void;
  
  // Behavior props
  multiple?: boolean;
  required?: boolean;
  disabled?: boolean;
  clearable?: boolean;
  searchable?: boolean;
  
  // Validation props
  error?: boolean;
  helperText?: string;
  
  // Styling props
  fullWidth?: boolean;
  size?: 'small' | 'medium';
  variant?: 'outlined' | 'filled' | 'standard';
  sx?: SxProps<Theme>;
  
  // Advanced props
  maxHeight?: number;
  renderValue?: (selected: string | number | string[] | number[]) => React.ReactNode;
  getOptionLabel?: (option: SelectOption) => string;
  getOptionValue?: (option: SelectOption) => string | number;
  
  // Event handlers
  onOpen?: () => void;
  onClose?: () => void;
  onBlur?: () => void;
  onFocus?: () => void;
  
  // Accessibility
  'aria-label'?: string;
  'aria-describedby'?: string;
  id?: string;
  name?: string;
}

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;

const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
      borderRadius: 12,
      marginTop: 4,
      boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
    },
  },
  anchorOrigin: {
    vertical: 'bottom' as const,
    horizontal: 'left' as const,
  },
  transformOrigin: {
    vertical: 'top' as const,
    horizontal: 'left' as const,
  },
};

export const Select = forwardRef<HTMLDivElement, SelectProps>(({
  label,
  placeholder,
  value = multiple ? [] : '',
  options = [],
  onChange,
  multiple = false,
  required = false,
  disabled = false,
  clearable = false,
  error = false,
  helperText,
  fullWidth = true,
  size = 'medium',
  variant = 'outlined',
  sx,
  maxHeight,
  renderValue,
  getOptionLabel = (option) => option.label,
  getOptionValue = (option) => option.value,
  onOpen,
  onClose,
  onBlur,
  onFocus,
  'aria-label': ariaLabel,
  'aria-describedby': ariaDescribedBy,
  id,
  name,
  ...props
}, ref) => {
  const handleChange = (event: SelectChangeEvent<string | number | string[] | number[]>) => {
    const newValue = event.target.value;
    onChange?.(newValue);
  };

  const renderMultipleValue = (selected: string | number | string[] | number[]) => {
    if (!Array.isArray(selected) || selected.length === 0) {
      return <em style={{ color: 'rgba(0, 0, 0, 0.6)' }}>{placeholder || 'Select options...'}</em>;
    }

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
        {selected.map((val) => {
          const option = options.find(opt => getOptionValue(opt) === val);
          return (
            <Chip
              key={val}
              label={option ? getOptionLabel(option) : val}
              size="small"
              sx={{
                height: 24,
                fontSize: '0.75rem',
                backgroundColor: 'primary.main',
                color: 'primary.contrastText',
                '& .MuiChip-deleteIcon': {
                  color: 'primary.contrastText',
                  '&:hover': {
                    color: 'primary.contrastText',
                  },
                },
              }}
            />
          );
        })}
      </Box>
    );
  };

  const renderSingleValue = (selected: string | number | string[] | number[]) => {
    if (!selected || (Array.isArray(selected) && selected.length === 0)) {
      return <em style={{ color: 'rgba(0, 0, 0, 0.6)' }}>{placeholder || 'Select an option...'}</em>;
    }
    
    const val = Array.isArray(selected) ? selected[0] : selected;
    const option = options.find(opt => getOptionValue(opt) === val);
    return option ? getOptionLabel(option) : val;
  };

  const defaultRenderValue = multiple ? renderMultipleValue : renderSingleValue;

  const menuPropsWithHeight = maxHeight ? {
    ...MenuProps,
    PaperProps: {
      ...MenuProps.PaperProps,
      style: {
        ...MenuProps.PaperProps.style,
        maxHeight: maxHeight,
      },
    },
  } : MenuProps;

  return (
    <FormControl
      ref={ref}
      fullWidth={fullWidth}
      error={error}
      required={required}
      disabled={disabled}
      size={size}
      variant={variant}
      sx={sx}
    >
      {label && (
        <InputLabel
          id={`${id || name}-label`}
          sx={{
            fontSize: size === 'small' ? '0.875rem' : '1rem',
            '&.Mui-focused': {
              color: 'primary.main',
            },
          }}
        >
          {label}
        </InputLabel>
      )}
      
      <MuiSelect
        labelId={`${id || name}-label`}
        id={id}
        name={name}
        multiple={multiple}
        value={value}
        onChange={handleChange}
        onOpen={onOpen}
        onClose={onClose}
        onBlur={onBlur}
        onFocus={onFocus}
        input={<OutlinedInput label={label} />}
        renderValue={renderValue || defaultRenderValue}
        MenuProps={menuPropsWithHeight}
        displayEmpty
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedBy}
        sx={{
          borderRadius: 2,
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: error ? 'error.main' : 'rgba(0, 0, 0, 0.23)',
            borderWidth: 1,
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: error ? 'error.main' : 'primary.main',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: error ? 'error.main' : 'primary.main',
            borderWidth: 2,
          },
          '& .MuiSelect-select': {
            padding: size === 'small' ? '8px 14px' : '12px 14px',
          },
        }}
        {...props}
      >
        {placeholder && !multiple && (
          <MenuItem value="" disabled sx={{ fontStyle: 'italic', color: 'text.secondary' }}>
            {placeholder}
          </MenuItem>
        )}
        
        {options.map((option) => {
          const optionValue = getOptionValue(option);
          const optionLabel = getOptionLabel(option);
          
          return (
            <MenuItem
              key={optionValue}
              value={optionValue}
              disabled={option.disabled}
              sx={{
                borderRadius: 1,
                margin: '2px 8px',
                '&:hover': {
                  backgroundColor: 'primary.light',
                  color: 'primary.contrastText',
                },
                '&.Mui-selected': {
                  backgroundColor: 'primary.main',
                  color: 'primary.contrastText',
                  '&:hover': {
                    backgroundColor: 'primary.dark',
                  },
                },
              }}
            >
              {multiple && (
                <Checkbox
                  checked={Array.isArray(value) && value.includes(optionValue)}
                  sx={{
                    color: 'primary.main',
                    '&.Mui-checked': {
                      color: 'primary.main',
                    },
                  }}
                />
              )}
              
              <ListItemText
                primary={optionLabel}
                secondary={option.description}
                sx={{
                  '& .MuiListItemText-primary': {
                    fontSize: size === 'small' ? '0.875rem' : '1rem',
                    fontWeight: 500,
                  },
                  '& .MuiListItemText-secondary': {
                    fontSize: '0.75rem',
                    color: 'text.secondary',
                  },
                }}
              />
            </MenuItem>
          );
        })}
      </MuiSelect>
      
      {helperText && (
        <FormHelperText
          sx={{
            fontSize: '0.75rem',
            marginTop: 1,
            color: error ? 'error.main' : 'text.secondary',
          }}
        >
          {helperText}
        </FormHelperText>
      )}
    </FormControl>
  );
});

Select.displayName = 'Select';

export default Select;