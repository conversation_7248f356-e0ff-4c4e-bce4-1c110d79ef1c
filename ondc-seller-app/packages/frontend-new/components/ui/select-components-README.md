# Material Design 3 Select Components

A comprehensive set of reusable select components built with Material-UI v7 and TypeScript, following Material Design 3 principles.

## 🚀 Quick Start

```tsx
import { SingleSelect, MultiSelect } from '@/components/ui';

// Single selection
<SingleSelect
  label="Country"
  placeholder="Select a country"
  value={country}
  onChange={setCountry}
  options={countries}
  clearable
/>

// Multiple selection
<MultiSelect
  label="Skills"
  placeholder="Select your skills"
  value={skills}
  onChange={setSkills}
  options={skillOptions}
  showSelectAll
  maxTags={3}
/>
```

## 📦 Components Included

### Core Components
- **`Select`** - Base component supporting both single and multiple selection
- **`SingleSelect`** - Specialized single selection with clearable option
- **`MultiSelect`** - Specialized multiple selection with chip display

### Form Integration
- **`FormSelect`** - React Hook Form integrated base component
- **`FormSingleSelect`** - React Hook Form integrated single select
- **`FormMultiSelect`** - React Hook Form integrated multi select

## ✨ Features

- 🎨 **Material Design 3** styling with theme integration
- 🔧 **TypeScript** support with full type safety
- 📱 **Responsive** design that works on all screen sizes
- ♿ **Accessible** with proper ARIA attributes and keyboard navigation
- 🎯 **React Hook Form** integration with validation support
- 🔍 **Searchable** options (coming soon)
- 🏷️ **Chip display** for multiple selections with overflow handling
- ✅ **Select All** functionality for multi-select
- 🧹 **Clearable** single selects
- 🎭 **Customizable** styling and theming
- 📝 **Rich options** with descriptions and disabled states

## 🛠️ Installation

The components are already included in your project. Simply import them:

```tsx
import { 
  Select, 
  SingleSelect, 
  MultiSelect,
  FormSingleSelect,
  FormMultiSelect 
} from '@/components/ui';
```

## 📖 Usage Examples

### Basic Single Select

```tsx
import { useState } from 'react';
import { SingleSelect, SelectOption } from '@/components/ui';

const countries: SelectOption[] = [
  { value: 'us', label: 'United States', description: 'North America' },
  { value: 'ca', label: 'Canada', description: 'North America' },
  { value: 'uk', label: 'United Kingdom', description: 'Europe' },
];

function CountrySelector() {
  const [country, setCountry] = useState('');

  return (
    <SingleSelect
      label="Country"
      placeholder="Select your country"
      value={country}
      onChange={setCountry}
      options={countries}
      clearable
      helperText="Choose your country of residence"
    />
  );
}
```

### Multi Select with Select All

```tsx
import { useState } from 'react';
import { MultiSelect } from '@/components/ui';

const skills = [
  { value: 'js', label: 'JavaScript' },
  { value: 'ts', label: 'TypeScript' },
  { value: 'react', label: 'React' },
  { value: 'node', label: 'Node.js' },
];

function SkillSelector() {
  const [selectedSkills, setSelectedSkills] = useState([]);

  return (
    <MultiSelect
      label="Technical Skills"
      placeholder="Select your skills"
      value={selectedSkills}
      onChange={setSelectedSkills}
      options={skills}
      showSelectAll
      selectAllText="Select All Skills"
      maxTags={2}
      chipVariant="outlined"
      chipColor="primary"
      helperText="Choose your technical skills"
    />
  );
}
```

### Form Integration with Validation

```tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FormSingleSelect, FormMultiSelect } from '@/components/ui';

const schema = z.object({
  country: z.string().min(1, 'Country is required'),
  skills: z.array(z.string())
    .min(1, 'At least one skill is required')
    .max(5, 'Maximum 5 skills allowed'),
});

type FormData = z.infer<typeof schema>;

function UserForm() {
  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      country: '',
      skills: [],
    },
  });

  const onSubmit = (data: FormData) => {
    console.log('Form data:', data);
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)}>
      <FormSingleSelect
        name="country"
        control={form.control}
        label="Country *"
        placeholder="Select your country"
        options={countries}
        clearable
      />

      <FormMultiSelect
        name="skills"
        control={form.control}
        label="Skills *"
        placeholder="Select your skills"
        options={skills}
        showSelectAll
        maxTags={3}
      />

      <button type="submit">Submit</button>
    </form>
  );
}
```

## 🎨 Customization

### Custom Styling

```tsx
<SingleSelect
  // ... other props
  sx={{
    '& .MuiOutlinedInput-root': {
      borderRadius: 3,
      '&:hover .MuiOutlinedInput-notchedOutline': {
        borderColor: 'secondary.main',
      },
    },
  }}
/>
```

### Custom Chip Colors

```tsx
<MultiSelect
  // ... other props
  chipVariant="outlined"
  chipColor="secondary"
  sx={{
    '& .MuiChip-root': {
      fontWeight: 600,
    },
  }}
/>
```

## 🔧 API Reference

### SelectOption Interface

```tsx
interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  description?: string;
}
```

### Common Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | - | Field label |
| `placeholder` | `string` | - | Placeholder text |
| `options` | `SelectOption[]` | `[]` | Available options |
| `disabled` | `boolean` | `false` | Disable the field |
| `required` | `boolean` | `false` | Mark as required |
| `error` | `boolean` | `false` | Show error state |
| `helperText` | `string` | - | Helper text |
| `size` | `'small' \| 'medium'` | `'medium'` | Field size |
| `fullWidth` | `boolean` | `true` | Full width |

### SingleSelect Specific Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `string \| number` | `''` | Selected value |
| `onChange` | `(value: string \| number \| null) => void` | - | Change handler |
| `clearable` | `boolean` | `false` | Show clear button |

### MultiSelect Specific Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `string[] \| number[]` | `[]` | Selected values |
| `onChange` | `(value: string[] \| number[]) => void` | - | Change handler |
| `showSelectAll` | `boolean` | `false` | Show select all option |
| `maxTags` | `number` | - | Max chips before "+X more" |
| `chipVariant` | `'filled' \| 'outlined'` | `'filled'` | Chip variant |
| `chipColor` | MUI colors | `'primary'` | Chip color |

## 🧪 Testing

To test the components, visit `/test-selects` in your application to see live examples and interact with all features.

## 📚 Documentation

For complete documentation, see [docs/select-components.md](../../docs/select-components.md).

## 🤝 Contributing

When contributing to these components:

1. Follow the existing code style and patterns
2. Add TypeScript types for all props
3. Include proper accessibility attributes
4. Test with keyboard navigation and screen readers
5. Update documentation and examples
6. Ensure Material Design 3 compliance

## 📄 License

These components are part of the ONDC Seller App project and follow the same license terms.