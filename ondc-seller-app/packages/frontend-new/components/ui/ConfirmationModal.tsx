'use client';

import React from 'react';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmButtonColor?: 'red' | 'blue' | 'green' | 'yellow';
  isLoading?: boolean;
  icon?: 'warning' | 'danger' | 'info' | 'success';
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmButtonColor = 'red',
  isLoading = false,
  icon = 'warning'
}) => {
  if (!isOpen) return null;

  const getIconComponent = () => {
    const iconClasses = {
      warning: 'h-6 w-6 text-yellow-600',
      danger: 'h-6 w-6 text-red-600',
      info: 'h-6 w-6 text-blue-600',
      success: 'h-6 w-6 text-green-600'
    };

    const bgClasses = {
      warning: 'bg-yellow-100',
      danger: 'bg-red-100',
      info: 'bg-blue-100',
      success: 'bg-green-100'
    };

    const iconPaths = {
      warning: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z",
      danger: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z",
      info: "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",
      success: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
    };

    return (
      <div className={`mx-auto flex items-center justify-center h-12 w-12 rounded-full ${bgClasses[icon]}`}>
        <svg className={iconClasses[icon]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={iconPaths[icon]} />
        </svg>
      </div>
    );
  };

  const getConfirmButtonClasses = () => {
    const baseClasses = "px-4 py-2 text-white text-base font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed";
    
    const colorClasses = {
      red: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
      blue: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
      green: 'bg-green-600 hover:bg-green-700 focus:ring-green-500',
      yellow: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
    };

    return `${baseClasses} ${colorClasses[confirmButtonColor]}`;
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-[9999]">
      <div className="relative top-[30%] mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3 text-center">
          {/* Icon */}
          {getIconComponent()}
          
          {/* Title */}
          <h3 className="text-lg leading-6 font-medium text-gray-900 mt-4">
            {title}
          </h3>
          
          {/* Message */}
          <div className="mt-2 px-7 py-3">
            <p className="text-sm text-gray-500">
              {message}
            </p>
          </div>
          
          {/* Action Buttons */}
          <div className="flex items-center justify-center space-x-4 mt-4">
            <button
              onClick={onClose}
              disabled={isLoading}
              className="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {cancelText}
            </button>
            <button
              onClick={onConfirm}
              disabled={isLoading}
              className={getConfirmButtonClasses()}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <svg className="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Loading...
                </div>
              ) : (
                confirmText
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;