'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useNavigationWithLoading } from '@/hooks/useNavigationWithLoading';
import { getBannersByStoreHandle, type BannerData, type StrapiResponse } from '@/lib/api/strapi/banners';
import { useToast } from '@/app/providers/toast-provider';

// Strapi base URL configuration
const STRAPI_BASE_URL = process.env.NEXT_PUBLIC_STRAPI_BASE_URL || 'http://localhost:1337';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  Tooltip,
  Fab,
  Container,
  Grid,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Image as ImageIcon,
  Link as LinkIcon,
  Title as TitleIcon
} from '@mui/icons-material';

interface Banner {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  buttonText: string;
  buttonLink: string;
  isActive: boolean;
  position: number;
  createdAt: string;
  updatedAt: string;
}

// Helper function to convert Strapi banner data to component format
const convertStrapiBannerToComponent = (strapiBanner: BannerData): Banner => {
  // Get image URL from either image_url field or nested image object
  let imageUrl = strapiBanner.image_url || strapiBanner.image?.url || '';
  
  // Add Strapi base URL if the image URL is relative
  if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('data:')) {
    imageUrl = `${STRAPI_BASE_URL}${imageUrl}`;
  }
  
  console.log('🖼️ Converting banner:', {
    title: strapiBanner.title,
    image_url: strapiBanner.image_url,
    nested_image_url: strapiBanner.image?.url,
    final_imageUrl: imageUrl,
    strapi_base_url: STRAPI_BASE_URL
  });
  
  return {
    id: strapiBanner.documentId, // Use documentId as the primary identifier
    title: strapiBanner.title,
    description: strapiBanner.description,
    imageUrl: imageUrl,
    buttonText: strapiBanner.buttonText || '',
    buttonLink: strapiBanner.buttonLink || strapiBanner.link || '',
    isActive: strapiBanner.active,
    position: strapiBanner.position,
    createdAt: strapiBanner.createdAt,
    updatedAt: strapiBanner.updatedAt
  };
};

// Mock data for demonstration (fallback)
const mockBanners: Banner[] = [
  {
    id: '1',
    title: 'Summer Sale 2024',
    description: 'Get up to 50% off on all summer collections. Limited time offer!',
    imageUrl: 'https://via.placeholder.com/800x400/4f46e5/ffffff?text=Summer+Sale',
    buttonText: 'Shop Now',
    buttonLink: '/collections/summer-sale',
    isActive: true,
    position: 1,
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-20T14:45:00Z'
  },
  {
    id: '2',
    title: 'New Arrivals',
    description: 'Discover our latest collection of trendy fashion items.',
    imageUrl: 'https://via.placeholder.com/800x400/059669/ffffff?text=New+Arrivals',
    buttonText: 'Explore',
    buttonLink: '/collections/new-arrivals',
    isActive: false,
    position: 2,
    createdAt: '2024-01-10T09:15:00Z',
    updatedAt: '2024-01-18T16:20:00Z'
  },
  {
    id: '3',
    title: 'Free Shipping',
    description: 'Free shipping on orders over $100. No code needed!',
    imageUrl: 'https://via.placeholder.com/800x400/dc2626/ffffff?text=Free+Shipping',
    buttonText: 'Start Shopping',
    buttonLink: '/products',
    isActive: true,
    position: 3,
    createdAt: '2024-01-05T11:00:00Z',
    updatedAt: '2024-01-22T13:30:00Z'
  }
];

export const AdminBannersList: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const storeHandle = params.storeHandle as string;
  const { showToast } = useToast();
  const { navigateWithLoading } = useNavigationWithLoading();
  
  const [banners, setBanners] = useState<Banner[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedBanner, setSelectedBanner] = useState<Banner | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [bannerToDelete, setBannerToDelete] = useState<Banner | null>(null);

  // Fetch banners from Strapi API
  useEffect(() => {
    const fetchBanners = async () => {
      if (!storeHandle) {
        console.warn('⚠️ No store handle provided, cannot fetch banners');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        
        const response: StrapiResponse<BannerData[]> = await getBannersByStoreHandle(storeHandle);
        
        // Convert Strapi data to component format
        const convertedBanners = response.data.map(convertStrapiBannerToComponent);
        setBanners(convertedBanners);
        
      } catch (error: any) {
        console.error('❌ Error fetching banners:', error);
        // Show error toast
        showToast(`Failed to fetch banners: ${error.message || 'Unknown error'}`, 'error');
        // Fallback to mock data in case of error
        setBanners(mockBanners);
        
      } finally {
        setIsLoading(false);
      }
    };

    fetchBanners();
  }, [storeHandle]);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, banner: Banner) => {
    setAnchorEl(event.currentTarget);
    setSelectedBanner(banner);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedBanner(null);
  };

  // Removed toggle functionality - status is now read-only

  const handleDeleteClick = (banner: Banner) => {
    setBannerToDelete(banner);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleDeleteConfirm = () => {
    if (bannerToDelete) {
      setBanners(prev => prev.filter(banner => banner.id !== bannerToDelete.id));
      
      // Show success toast
      showToast(`Banner "${bannerToDelete.title}" deleted successfully`, 'success');
      
      setDeleteDialogOpen(false);
      setBannerToDelete(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Show loading state
  if (isLoading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="400px">
          <CircularProgress size={48} sx={{ mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            Loading banners...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Fetching banners for {storeHandle}
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Page Header */}
      <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={4}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight={600}>
            Banner Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Create and manage promotional banners for your store
          </Typography>
        </Box>
        
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          size="large"
          onClick={() => navigateWithLoading(`/${storeHandle}/admin/banners/new`)}
          sx={{
            borderRadius: 2,
            textTransform: 'none',
            px: 3
          }}
        >
          Create Banner
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1} sx={{ borderRadius: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Avatar sx={{ bgcolor: 'primary.main', width: 48, height: 48 }}>
                  <ImageIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {banners.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Banners
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1} sx={{ borderRadius: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Avatar sx={{ bgcolor: 'success.main', width: 48, height: 48 }}>
                  <VisibilityIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {banners.filter(b => b.isActive).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Banners
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1} sx={{ borderRadius: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Avatar sx={{ bgcolor: 'warning.main', width: 48, height: 48 }}>
                  <VisibilityOffIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {banners.filter(b => !b.isActive).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Inactive Banners
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1} sx={{ borderRadius: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Avatar sx={{ bgcolor: 'info.main', width: 48, height: 48 }}>
                  <LinkIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {banners.filter(b => b.buttonLink).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    With CTA Links
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Empty State */}
      {banners.length === 0 && (
        <Card elevation={1} sx={{ borderRadius: 2, mb: 4 }}>
          <CardContent sx={{ py: 8, textAlign: 'center' }}>
            <ImageIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              No banners found
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Create your first promotional banner to get started
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigateWithLoading(`/${storeHandle}/admin/banners/new`)}
            >
              Create Your First Banner
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Banners Table */}
      {banners.length > 0 && (
        <Card elevation={1} sx={{ borderRadius: 2 }}>
          <CardContent sx={{ p: 0 }}>
            <TableContainer>
              <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: 'grey.50' }}>
                  <TableCell sx={{ fontWeight: 600 }}>Banner</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Content</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Call to Action</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                  {/* <TableCell sx={{ fontWeight: 600 }}>Last Updated</TableCell> */}
                  <TableCell sx={{ fontWeight: 600, textAlign: 'center' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {banners.map((banner) => (
                  <TableRow key={banner.id} hover>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={2}>
                        <Avatar
                          src={banner.imageUrl}
                          variant="rounded"
                          sx={{ width: 60, height: 40 }}
                        >
                          <ImageIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2" fontWeight={600}>
                            {banner.title}
                          </Typography>
                          {/* <Typography variant="caption" color="text.secondary">
                            ID: {banner.id}
                          </Typography> */}
                        </Box>
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      <Box sx={{ maxWidth: 300 }}>
                        <Typography variant="body2" noWrap>
                          {banner.description}
                        </Typography>
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight={500}>
                          {banner.buttonText}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" noWrap>
                          {banner.buttonLink}
                        </Typography>
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      <Chip
                        label={banner.isActive ? 'Active' : 'Inactive'}
                        color={banner.isActive ? 'success' : 'default'}
                        size="small"
                        variant={banner.isActive ? 'filled' : 'outlined'}
                      />
                    </TableCell>
                    
                   
                    
                    <TableCell align="center">
                      <Tooltip title="More actions">
                        <IconButton
                          onClick={(e) => handleMenuOpen(e, banner)}
                          size="small"
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => {
          if (selectedBanner) {
            navigateWithLoading(`/${storeHandle}/admin/banners/${selectedBanner.id}`);
          }
          handleMenuClose();
        }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Banner</ListItemText>
        </MenuItem>
      
        <MenuItem 
          onClick={() => selectedBanner && handleDeleteClick(selectedBanner)}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Delete Banner
        </DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the banner "{bannerToDelete?.title}"? 
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

    
    </Container>
  );
};