'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useNavigationWithLoading } from '@/hooks/useNavigationWithLoading';
import { useAuthStore } from '@/stores/authStore';
import { medusaAdminService, type MedusaTag } from '@/lib/api/medusa-admin';
import { useToast } from '@/app/providers/toast-provider';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';

// Material Design 3 Components
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Chip,
  IconButton,
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Toolbar,
  Alert,
  AlertTitle,
  Skeleton,
  Stack,
  Divider,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Fab,
  Tooltip,
  InputAdornment,
  Container,
  Grid,
  Pagination,
  FormControlLabel,
  Switch,
  Breadcrumbs,
  Link as MuiLink,
} from '@mui/material';

import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  FilterList as FilterListIcon,
  ViewList as ViewListIcon,
  ViewModule as ViewModuleIcon,
  LocalOffer as TagIcon,
  Refresh as RefreshIcon,
  Download as ExportIcon,
  Upload as ImportIcon,
  Settings as SettingsIcon,
  NavigateNext as NavigateNextIcon,
} from '@mui/icons-material';

export const AdminTagsListMD3: React.FC = () => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const { getStoreHandle } = useAuthStore();
  const { showToast } = useToast();
  const { navigateWithLoading } = useNavigationWithLoading();
  
  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [tags, setTags] = useState<MedusaTag[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [deletingTagId, setDeletingTagId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [page, setPage] = useState(1);
  const [rowsPerPage] = useState(10);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTagForMenu, setSelectedTagForMenu] = useState<string | null>(null);
  
  const [deleteConfirmModal, setDeleteConfirmModal] = useState<{
    isOpen: boolean;
    tagId: string;
    tagName: string;
  }>({ isOpen: false, tagId: '', tagName: '' });
  
  // Get dynamic store handle from auth store
  const dynamicStoreHandle = getStoreHandle() || storeHandle;

  // Fetch tags from API
  useEffect(() => {
    const fetchTags = async () => {
      if (!dynamicStoreHandle) {
        console.log('No store handle available for tags API call');
        setIsLoading(false);
        return;
      }
      
      setIsLoading(true);
      setError(null);
      
      try {
        console.log('=== FETCHING TAGS ===');
        console.log('Using store handle as x-tenant-id:', dynamicStoreHandle);
        
        const tagsArray = await medusaAdminService.getTags(dynamicStoreHandle);
        
        console.log('Tags fetched successfully:', tagsArray);
        
        // Filter tags by search term if provided
        let filteredTags = tagsArray;
        if (searchTerm) {
          filteredTags = tagsArray.filter(tag => 
            tag.value.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        
        setTags(filteredTags);
        setTotalCount(tagsArray.length);
      } catch (error: any) {
        console.error('Failed to fetch tags:', error);
        setError(error.message || 'Failed to fetch tags');
        setTags([]);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchTags();
  }, [dynamicStoreHandle, searchTerm]);

  // Tags are already filtered by the API based on search
  const filteredTags = tags;

  const handleSelectAll = () => {
    if (selectedTags.length === filteredTags.length) {
      setSelectedTags([]);
    } else {
      setSelectedTags(filteredTags.map(t => t.id));
    }
  };

  const handleSelectTag = (tagId: string) => {
    setSelectedTags(prev =>
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };

  const handleDeleteClick = (tagId: string, tagValue: string) => {
    setDeleteConfirmModal({
      isOpen: true,
      tagId,
      tagName: tagValue
    });
  };

  const handleDeleteConfirm = async () => {
    const { tagId } = deleteConfirmModal;
    
    setDeletingTagId(tagId);
    try {
      await medusaAdminService.deleteTag(dynamicStoreHandle, tagId);
      showToast('Tag deleted successfully', 'success');
      
      // Remove the deleted tag from the local state
      setTags(prev => prev.filter(tag => tag.id !== tagId));
      setSelectedTags(prev => prev.filter(id => id !== tagId));
      setTotalCount(prev => prev - 1);
      
      // Close the modal
      setDeleteConfirmModal({ isOpen: false, tagId: '', tagName: '' });
    } catch (error: any) {
      console.error('Error deleting tag:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete tag';
      showToast(errorMessage, 'error');
    } finally {
      setDeletingTagId(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmModal({ isOpen: false, tagId: '', tagName: '' });
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, tagId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedTagForMenu(tagId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedTagForMenu(null);
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  const renderTableSkeleton = () => (
    <TableContainer component={Paper} elevation={0} sx={{ border: 1, borderColor: 'divider' }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell padding="checkbox">
              <Skeleton variant="rectangular" width={20} height={20} />
            </TableCell>
            <TableCell>Tag Name</TableCell>
            <TableCell>Created</TableCell>
            <TableCell align="right">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {[...Array(5)].map((_, index) => (
            <TableRow key={index}>
              <TableCell padding="checkbox">
                <Skeleton variant="rectangular" width={20} height={20} />
              </TableCell>
              <TableCell>
                <Skeleton variant="rounded" width={120} height={32} />
              </TableCell>
              <TableCell>
                <Skeleton variant="text" width={100} />
              </TableCell>
              <TableCell align="right">
                <Skeleton variant="text" width={80} />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const renderGridSkeleton = () => (
    <Grid container spacing={3}>
      {[...Array(8)].map((_, index) => (
        <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
          <Card elevation={1}>
            <CardContent>
              <Skeleton variant="rounded" width="100%" height={32} sx={{ mb: 2 }} />
              <Skeleton variant="text" width="60%" />
              <Skeleton variant="text" width="40%" />
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  const renderTagCard = (tag: MedusaTag) => (
    <Grid item xs={12} sm={6} md={4} lg={3} key={tag.id}>
      <Card 
        elevation={1}
        sx={{ 
          height: '100%',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            elevation: 3,
            transform: 'translateY(-2px)',
          }
        }}
      >
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
            <Chip
              label={tag.value}
              color="primary"
              variant="outlined"
              size="medium"
              sx={{ fontWeight: 500 }}
            />
            <Box>
              <Checkbox
                checked={selectedTags.includes(tag.id)}
                onChange={() => handleSelectTag(tag.id)}
                size="small"
              />
              <IconButton
                size="small"
                onClick={(e) => handleMenuOpen(e, tag.id)}
              >
                <MoreVertIcon />
              </IconButton>
            </Box>
          </Box>
          
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Created: {tag.created_at ? new Date(tag.created_at).toLocaleDateString('en-IN', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            }) : 'Date not available'}
          </Typography>
          
          <Box display="flex" gap={1} mt={2}>
            <Button
              onClick={() => navigateWithLoading(`/${dynamicStoreHandle}/admin/tags/${tag.id}`)}
              size="small"
              variant="outlined"
              startIcon={<EditIcon />}
              fullWidth
            >
              Edit
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Grid>
  );

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />}
        sx={{ mb: 3 }}
      >
        <MuiLink 
          component={Link} 
          href={`/${dynamicStoreHandle}/admin`}
          underline="hover"
          color="inherit"
        >
          Admin
        </MuiLink>
        <Typography color="text.primary">Tags</Typography>
      </Breadcrumbs>

      {/* Page Header */}
      <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={4}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight={600}>
            Tags
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage product tags and labels to help customers find products
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <Tooltip title="Refresh">
            <IconButton onClick={handleRefresh} color="primary">
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Button
            onClick={() => navigateWithLoading(`/${dynamicStoreHandle}/admin/tags/new`)}
            variant="contained"
            startIcon={<AddIcon />}
            size="large"
          >
            Add Tag
          </Button>
        </Stack>
      </Box>

      {/* Search and Filters */}
      <Card elevation={1} sx={{ mb: 3 }}>
        <CardContent>
          <Stack direction={{ xs: 'column', md: 'row' }} spacing={2} alignItems="center">
            <TextField
              placeholder="Search tags..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              variant="outlined"
              size="medium"
              sx={{ flexGrow: 1, minWidth: 300 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
              }}
            />
            
            <Stack direction="row" spacing={1} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                View:
              </Typography>
              <IconButton
                onClick={() => setViewMode('list')}
                color={viewMode === 'list' ? 'primary' : 'default'}
                size="small"
              >
                <ViewListIcon />
              </IconButton>
              <IconButton
                onClick={() => setViewMode('grid')}
                color={viewMode === 'grid' ? 'primary' : 'default'}
                size="small"
              >
                <ViewModuleIcon />
              </IconButton>
              
              <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
              
              <Typography variant="body2" color="text.secondary">
                {totalCount > 0 ? `${totalCount} total tags` : `${filteredTags.length} tags`}
              </Typography>
            </Stack>
          </Stack>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedTags.length > 0 && (
        <Card elevation={1} sx={{ mb: 3, bgcolor: 'primary.50', borderColor: 'primary.200' }}>
          <CardContent>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="subtitle1" color="primary.main" fontWeight={500}>
                {selectedTags.length} tag(s) selected
              </Typography>
              <Stack direction="row" spacing={1}>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<EditIcon />}
                >
                  Bulk Edit
                </Button>
                <Button
                  variant="contained"
                  color="error"
                  size="small"
                  startIcon={<DeleteIcon />}
                >
                  Delete Selected
                </Button>
              </Stack>
            </Stack>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} action={
          <Button color="inherit" size="small" onClick={handleRefresh}>
            Retry
          </Button>
        }>
          <AlertTitle>Error loading tags</AlertTitle>
          {error}
        </Alert>
      )}

      {/* Content */}
      {isLoading ? (
        viewMode === 'list' ? renderTableSkeleton() : renderGridSkeleton()
      ) : error ? null : (
        <>
          {filteredTags.length > 0 ? (
            <>
              {viewMode === 'list' ? (
                <TableContainer component={Paper} elevation={1}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell padding="checkbox">
                          <Checkbox
                            checked={selectedTags.length === filteredTags.length && filteredTags.length > 0}
                            indeterminate={selectedTags.length > 0 && selectedTags.length < filteredTags.length}
                            onChange={handleSelectAll}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" fontWeight={600}>
                            Tag Name
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" fontWeight={600}>
                            Created
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="subtitle2" fontWeight={600}>
                            Actions
                          </Typography>
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredTags.map((tag) => (
                        <TableRow 
                          key={tag.id}
                          hover
                          selected={selectedTags.includes(tag.id)}
                        >
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={selectedTags.includes(tag.id)}
                              onChange={() => handleSelectTag(tag.id)}
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={tag.value || 'Unnamed Tag'}
                              color="primary"
                              variant="outlined"
                              size="medium"
                              sx={{ fontWeight: 500 }}
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {tag.created_at ? new Date(tag.created_at).toLocaleDateString('en-IN', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                              }) : 'Date not available'}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Stack direction="row" spacing={1} justifyContent="flex-end">
                              <Button
                                onClick={() => navigateWithLoading(`/${dynamicStoreHandle}/admin/tags/${tag.id}`)}
                                size="small"
                                variant="outlined"
                                startIcon={<EditIcon />}
                              >
                                Edit
                              </Button>
                              <IconButton
                                onClick={(e) => handleMenuOpen(e, tag.id)}
                                size="small"
                              >
                                <MoreVertIcon />
                              </IconButton>
                            </Stack>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Grid container spacing={3}>
                  {filteredTags.map(renderTagCard)}
                </Grid>
              )}

              {/* Pagination */}
              {filteredTags.length > rowsPerPage && (
                <Box display="flex" justifyContent="center" mt={4}>
                  <Pagination
                    count={Math.ceil(filteredTags.length / rowsPerPage)}
                    page={page}
                    onChange={(_, newPage) => setPage(newPage)}
                    color="primary"
                    size="large"
                  />
                </Box>
              )}
            </>
          ) : (
            /* No Tags Message */
            <Paper elevation={1} sx={{ p: 8, textAlign: 'center' }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  maxWidth: 400,
                  mx: 'auto',
                }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: 'grey.100',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 3,
                  }}
                >
                  <TagIcon sx={{ fontSize: 40, color: 'grey.400' }} />
                </Box>
                
                <Typography variant="h5" gutterBottom fontWeight={600}>
                  {searchTerm ? 'No tags found' : 'No tags available'}
                </Typography>
                
                <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                  {searchTerm 
                    ? 'Try adjusting your search criteria to find tags.'
                    : 'Get started by creating your first product tag to help organize and categorize your products.'
                  }
                </Typography>
                
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                  {searchTerm && (
                    <Button
                      onClick={() => setSearchTerm('')}
                      variant="outlined"
                      size="large"
                    >
                      Clear Search
                    </Button>
                  )}
                  <Button
                    onClick={() => navigateWithLoading(`/${dynamicStoreHandle}/admin/tags/new`)}
                    variant="contained"
                    size="large"
                    startIcon={<AddIcon />}
                  >
                    {searchTerm ? 'Add New Tag' : 'Add Your First Tag'}
                  </Button>
                </Stack>
              </Box>
            </Paper>
          )}
        </>
      )}

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem
          onClick={() => {
            if (selectedTagForMenu) {
              navigateWithLoading(`/${dynamicStoreHandle}/admin/tags/${selectedTagForMenu}`);
            }
            handleMenuClose();
          }}
        >
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Tag</ListItemText>
        </MenuItem>
        <MenuItem
          onClick={() => {
            if (selectedTagForMenu) {
              const tag = filteredTags.find(t => t.id === selectedTagForMenu);
              if (tag) {
                handleDeleteClick(tag.id, tag.value);
              }
            }
            handleMenuClose();
          }}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete Tag</ListItemText>
        </MenuItem>
      </Menu>

      {/* Floating Action Button for Mobile */}
      <Fab
        color="primary"
        onClick={() => navigateWithLoading(`/${dynamicStoreHandle}/admin/tags/new`)}
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          display: { xs: 'flex', md: 'none' },
        }}
      >
        <AddIcon />
      </Fab>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteConfirmModal.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Tag"
        message={`Are you sure you want to delete the tag "${deleteConfirmModal.tagName}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonColor="red"
        icon="danger"
        isLoading={deletingTagId === deleteConfirmModal.tagId}
      />
    </Container>
  );
};