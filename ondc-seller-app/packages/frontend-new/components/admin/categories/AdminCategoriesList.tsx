'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useNavigationWithLoading } from '@/hooks/useNavigationWithLoading';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  Chip,
  IconButton,
  Stack,
  Alert,
  CircularProgress,
  InputAdornment,
  Pagination,
} from '@mui/material';
import {
  Add,
  Search,
  Edit,
  Delete,
  Refresh,
  Category,
  Error,
} from '@mui/icons-material';
import { AdminTableLoading } from '../AdminLoading';
import { useAuthStore } from '@/stores/authStore';
import { medusaAdminService, type MedusaCategory } from '@/lib/api/medusa-admin';
import { useToast } from '@/app/providers/toast-provider';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';

// Removed mock data - now using API

export const AdminCategoriesList: React.FC = () => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const { getStoreHandle } = useAuthStore();
  const { showToast } = useToast();
  const { navigateWithLoading } = useNavigationWithLoading();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [categories, setCategories] = useState<MedusaCategory[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [deletingCategoryId, setDeletingCategoryId] = useState<string | null>(null);
  const [deleteConfirmModal, setDeleteConfirmModal] = useState<{
    isOpen: boolean;
    categoryId: string;
    categoryName: string;
  }>({ isOpen: false, categoryId: '', categoryName: '' });
  
  // Get dynamic store handle from auth store
  const dynamicStoreHandle = getStoreHandle() || storeHandle;

  // Fetch categories from API
  useEffect(() => {
    const fetchCategories = async () => {
      if (!dynamicStoreHandle) {
        console.log('No store handle available for categories API call');
        setIsLoading(false);
        return;
      }
      
      setIsLoading(true);
      setError(null);
      
      try {
        console.log('=== FETCHING CATEGORIES ===');
        console.log('Using store handle as x-tenant-id:', dynamicStoreHandle);
        
        const categoriesArray = await medusaAdminService.getCategories(dynamicStoreHandle);
        
        console.log('Categories fetched successfully:', categoriesArray);
        
        // Filter categories by search term and status if provided
        let filteredCategories = categoriesArray;
        if (searchTerm) {
          filteredCategories = categoriesArray.filter(category => 
            category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (category.handle && category.handle.toLowerCase().includes(searchTerm.toLowerCase()))
          );
        }
        
        // Note: Medusa categories don't have a status field like 'active/inactive'
        // so we'll ignore the status filter for now
        
        setCategories(filteredCategories);
        setTotalCount(categoriesArray.length);
      } catch (error: any) {
        console.error('Failed to fetch categories:', error);
        setError(error.message || 'Failed to fetch categories');
        setCategories([]);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCategories();
  }, [dynamicStoreHandle, searchTerm, statusFilter]);

  // Categories are already filtered by the API based on search and status
  const filteredCategories = categories;

  const handleSelectAll = () => {
    if (selectedCategories.length === filteredCategories.length) {
      setSelectedCategories([]);
    } else {
      setSelectedCategories(filteredCategories.map(c => c.id));
    }
  };

  const handleSelectCategory = (categoryId: string) => {
    setSelectedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const handleDeleteClick = (categoryId: string, categoryName: string) => {
    setDeleteConfirmModal({
      isOpen: true,
      categoryId,
      categoryName
    });
  };

  const handleDeleteConfirm = async () => {
    const { categoryId } = deleteConfirmModal;
    
    setDeletingCategoryId(categoryId);
    try {
      await medusaAdminService.deleteCategory(dynamicStoreHandle, categoryId);
      showToast('Category deleted successfully', 'success');
      
      // Remove the deleted category from the local state
      setCategories(prev => prev.filter(category => category.id !== categoryId));
      setSelectedCategories(prev => prev.filter(id => id !== categoryId));
      setTotalCount(prev => prev - 1);
      
      // Close the modal
      setDeleteConfirmModal({ isOpen: false, categoryId: '', categoryName: '' });
    } catch (error: any) {
      console.error('Error deleting category:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete category';
      showToast(errorMessage, 'error');
    } finally {
      setDeletingCategoryId(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmModal({ isOpen: false, categoryId: '', categoryName: '' });
  };

  // Helper function to safely get category field values for MedusaCategory
  const getCategoryField = (category: MedusaCategory, field: string) => {
    switch (field) {
      case 'id':
        return category.id;
      case 'name':
        return category.name || 'Unnamed Category';
      case 'slug':
        return category.handle || 'N/A';
      case 'description':
        return category.description || '';
      case 'productCount':
        return 0; // Medusa doesn't return product count in basic response
      case 'status':
        return 'active'; // Default status since Medusa doesn't have status field
      case 'parentId':
        return category.parent_category_id;
      case 'createdAt':
        return category.created_at;
      default:
        return (category as any)[field] || null;
    }
  };

  const getStatusBadge = (status: string) => {
    return status === 'active' 
      ? 'bg-green-100 text-green-800' 
      : 'bg-gray-100 text-gray-800';
  };

  // Helper function to find parent category name
  const getParentCategoryName = (parentId?: string) => {
    if (!parentId) return null;
    const parentCategory = categories.find(cat => cat.id === parentId);
    return parentCategory?.name || 'Unknown Parent';
  };

  return (
    <Stack spacing={3} sx={{p: 3}}>
      {/* Page Header */}
      <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Categories
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Organize your products with categories
          </Typography>
        </Box>
        <Button
          onClick={() => navigateWithLoading(`/${dynamicStoreHandle}/admin/categories/new`)}
          variant="contained"
          startIcon={<Add />}
          size="large"
        >
          Add Category
        </Button>
      </Stack>

      {/* Filters and Search */}
      <Card>
        <CardContent>
          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            alignItems={{ xs: 'stretch', sm: 'center' }}
            justifyContent="space-between"
          >
            <Stack direction="row" spacing={2} alignItems="center">
              <TextField
                placeholder="Search categories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
                sx={{ minWidth: 250 }}
              /></Stack>

            <Typography variant="body2" color="text.secondary">
              {totalCount > 0 ? `${totalCount} total categories` : `${filteredCategories.length} categories`}
            </Typography>
          </Stack>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedCategories.length > 0 && (
        <Alert severity="info" sx={{ bgcolor: 'primary.50' }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="body2" sx={{ fontWeight: 600 }}>
              {selectedCategories.length} category(ies) selected
            </Typography>
            <Stack direction="row" spacing={1}>
              <Button variant="outlined" size="small">
                Bulk Edit
              </Button>
              <Button variant="outlined" size="small">
                Deactivate
              </Button>
              <Button variant="contained" color="error" size="small">
                Delete
              </Button>
            </Stack>
          </Stack>
        </Alert>
      )}

      {/* Error Display */}
      {error && (
        <Alert severity="error" action={
          <Button color="inherit" size="small" onClick={() => window.location.reload()}>
            Retry
          </Button>
        }>
          <Typography variant="body2">
            <strong>Error loading categories:</strong> {error}
          </Typography>
        </Alert>
      )}

      {/* Categories Table */}
      {isLoading ? (
        <AdminTableLoading />
      ) : error ? null : filteredCategories.length === 0 ? (
        /* No Categories Message */
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <Box sx={{
              mx: 'auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 64,
              height: 64,
              borderRadius: '50%',
              bgcolor: 'grey.100',
              mb: 2
            }}>
              <Category sx={{ fontSize: 32, color: 'text.secondary' }} />
            </Box>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
              {searchTerm || statusFilter !== 'all' ? 'No categories found' : 'No categories available'}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              {searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your search criteria or filters to find categories.'
                : 'Get started by creating your first product category.'
              }
            </Typography>
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center">
              {(searchTerm || statusFilter !== 'all') && (
                <Button
                  variant="outlined"
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('all');
                  }}
                >
                  Clear Filters
                </Button>
              )}
              <Button
                onClick={() => navigateWithLoading(`/${dynamicStoreHandle}/admin/categories/new`)}
                variant="contained"
                startIcon={<Add />}
              >
                Add Your First Category
              </Button>
            </Stack>
          </CardContent>
        </Card>
      ) : (
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  checked={selectedCategories.length === filteredCategories.length && filteredCategories.length > 0}
                  onChange={handleSelectAll}
                  indeterminate={selectedCategories.length > 0 && selectedCategories.length < filteredCategories.length}
                />
              </TableCell>
              <TableCell>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Category
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Handle
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Parent Category
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Created
                </Typography>
              </TableCell>
              <TableCell align="right">
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Actions
                </Typography>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredCategories.map((category) => {
              const categoryId = category.id;
              const categoryName = category.name || 'Unnamed Category';
              const categorySlug = category.handle || 'N/A';
              const categoryDescription = category.description || '';
              const categoryProductCount = 0; // Medusa doesn't return product count in basic response
              const categoryStatus = 'active'; // Default status since Medusa doesn't have status field
              const categoryParentId = category.parent_category_id;
              const categoryCreatedAt = category.created_at;
              const parentCategoryName = getParentCategoryName(categoryParentId);

              return (
                <TableRow key={categoryId} hover>
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedCategories.includes(categoryId)}
                      onChange={() => handleSelectCategory(categoryId)}
                    />
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {categoryName}
                      </Typography>
                      {/* {categoryDescription && (
                        <Typography variant="caption" color="text.secondary">
                          {categoryDescription}
                        </Typography>
                      )} */}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">{categorySlug}</Typography>
                  </TableCell>
                  <TableCell>
                    {parentCategoryName ? (
                      <Chip
                        label={parentCategoryName}
                        size="small"
                        variant="outlined"
                        color="primary"
                      />
                    ) : (
                      <Typography variant="body2" color="text.disabled">-</Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {categoryCreatedAt ? (
                        new Date(categoryCreatedAt).toLocaleDateString('en-IN', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                        })
                      ) : (
                        'Date not available'
                      )}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Stack direction="row" spacing={1} justifyContent="flex-end">
                      <IconButton
                        onClick={() => navigateWithLoading(`/${dynamicStoreHandle}/admin/categories/${categoryId}`)}
                        size="small"
                        color="primary"
                      >
                        <Edit />
                      </IconButton>
                      <IconButton
                        onClick={() => handleDeleteClick(categoryId, categoryName)}
                        disabled={deletingCategoryId === categoryId}
                        size="small"
                        color="error"
                      >
                        {deletingCategoryId === categoryId ? <CircularProgress size={16} /> : <Delete />}
                      </IconButton>
                    </Stack>
                  </TableCell>
                </TableRow>
                );
              })}
          </TableBody>
        </Table>

        {/* Pagination */}
        <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="body2" color="text.secondary">
              Showing 1 to {filteredCategories.length} of {filteredCategories.length} results
            </Typography>
            <Pagination
              count={1}
              page={1}
              size="small"
              showFirstButton
              showLastButton
            />
          </Stack>
        </Box>
      </TableContainer>
      )}

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteConfirmModal.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Category"
        message={`Are you sure you want to delete the category "${deleteConfirmModal.categoryName}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonColor="red"
        icon="danger"
        isLoading={deletingCategoryId === deleteConfirmModal.categoryId}
      />
    </Stack>
  );
};