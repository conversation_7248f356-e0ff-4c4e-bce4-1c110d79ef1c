'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useNavigationWithLoading } from '@/hooks/useNavigationWithLoading';
import { getCMSPagesByStoreHandle, deleteCMSPageById, type CMSPageData, type StrapiResponse } from '@/lib/api/strapi/pages';
import { useToast } from '@/app/providers/toast-provider';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  Tooltip,
  Fab,
  Container,
  Grid,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Article as ArticleIcon,
  Public as PublicIcon,
  Description as DraftIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';

interface CMSPage {
  id: string;
  title: string;
  slug: string;
  content: string;
  status: 'published' | 'draft' | 'scheduled';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  wordCount: number;
}

// Helper function to convert Strapi CMS page data to component format
const convertStrapiCMSPageToComponent = (strapiPage: CMSPageData): CMSPage => {
  // Safely handle content that might be null/undefined
  const safeContent = strapiPage.content || '';
  
  // Calculate word count from content
  const wordCount = safeContent ? safeContent.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length : 0;
  
  // Determine status based on publishedAt field
  const status = strapiPage.publishedAt ? 'published' : 'draft';
  
  console.log('📄 Converting CMS page:', {
    title: strapiPage.pageName || 'Untitled',
    documentId: strapiPage.documentId,
    publishedAt: strapiPage.publishedAt,
    determinedStatus: status,
    wordCount: wordCount
  });

  return {
    id: strapiPage.documentId || strapiPage.id?.toString() || 'unknown', // Fallback to id if documentId is missing
    title: strapiPage.pageName || 'Untitled',
    slug: strapiPage.pageSlug || '',
    content: safeContent,
    status: status, // Status determined by publishedAt field
    isActive: strapiPage.isActive ?? true, // Default to true if undefined
    createdAt: strapiPage.createdAt || new Date().toISOString(),
    updatedAt: strapiPage.updatedAt || new Date().toISOString(),
    publishedAt: strapiPage.publishedAt,
    wordCount: wordCount
  };
};

// Mock data for demonstration
const mockCMSPages: CMSPage[] = [
  {
    id: '1',
    title: 'About Us',
    slug: 'about-us',
    content: '<p>Learn more about our company, mission, and values. We are committed to providing the best products and services to our customers.</p>',
    status: 'published',
    isActive: true,
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-20T14:45:00Z',
    publishedAt: '2024-01-20T14:45:00Z',
    wordCount: 156
  },
  {
    id: '2',
    title: 'Terms & Conditions',
    slug: 'terms-conditions',
    content: '<p>Please read these terms and conditions carefully before using our service. By accessing or using our service, you agree to be bound by these terms.</p>',
    status: 'published',
    isActive: true,
    createdAt: '2024-01-10T09:15:00Z',
    updatedAt: '2024-01-18T16:20:00Z',
    publishedAt: '2024-01-18T16:20:00Z',
    wordCount: 1247
  },
  {
    id: '3',
    title: 'Privacy Policy',
    slug: 'privacy-policy',
    content: '<p>This Privacy Policy describes how we collect, use, and protect your personal information when you use our services.</p>',
    status: 'draft',
    isActive: false,
    createdAt: '2024-01-05T11:00:00Z',
    updatedAt: '2024-01-22T13:30:00Z',
    wordCount: 892
  },
  {
    id: '4',
    title: 'Refund Policy',
    slug: 'refund-policy',
    content: '<p>Our refund policy outlines the conditions under which refunds are processed and the timeline for refund requests.</p>',
    status: 'published',
    isActive: true,
    createdAt: '2024-01-08T14:20:00Z',
    updatedAt: '2024-01-25T10:15:00Z',
    publishedAt: '2024-01-25T10:15:00Z',
    wordCount: 634
  },
  {
    id: '5',
    title: 'Contact Us',
    slug: 'contact-us',
    content: '<p>Get in touch with our team. We are here to help you with any questions or concerns you may have.</p>',
    status: 'scheduled',
    isActive: true,
    createdAt: '2024-01-12T16:45:00Z',
    updatedAt: '2024-01-26T09:30:00Z',
    wordCount: 298
  }
];

export const AdminCMSPagesList: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const storeHandle = params.storeHandle as string;
  const { showToast } = useToast();
  const { navigateWithLoading } = useNavigationWithLoading();
  
  const [pages, setPages] = useState<CMSPage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPage, setSelectedPage] = useState<CMSPage | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [pageToDelete, setPageToDelete] = useState<CMSPage | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch CMS pages from Strapi API
  useEffect(() => {
    const fetchCMSPages = async () => {
      if (!storeHandle) {
        console.warn('⚠️ No store handle provided, cannot fetch CMS pages');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        
        console.log('🔄 Fetching CMS pages for store:', storeHandle);
        const response: StrapiResponse<CMSPageData[]> = await getCMSPagesByStoreHandle(storeHandle);
        
        console.log('✅ CMS pages fetched successfully:', response);
        console.log('🔍 Raw API response structure:', {
          dataType: typeof response.data,
          isArray: Array.isArray(response.data),
          dataLength: response.data?.length,
          firstItem: response.data?.[0],
          fullResponse: response
        });
        
        // Ensure we have valid data
        if (!response.data || !Array.isArray(response.data)) {
          console.warn('⚠️ Invalid API response structure:', response);
          throw new Error('Invalid API response: expected array of pages');
        }
        
        // Convert Strapi data to component format with error handling
        const convertedPages = response.data.map((page, index) => {
          try {
            return convertStrapiCMSPageToComponent(page);
          } catch (error) {
            console.error(`❌ Error converting page at index ${index}:`, error, page);
            // Return a safe fallback page
            return {
              id: `error-${index}`,
              title: 'Error Loading Page',
              slug: 'error',
              content: '',
              status: 'draft' as const,
              isActive: false,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              wordCount: 0
            };
          }
        });
        
        setPages(convertedPages);
        
        
      } catch (error: any) {
        console.error('❌ Error fetching CMS pages:', error);
        
        // Check if it's an authentication error
        if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
          showToast('Authentication failed. Please check STRAPI_ACCESS_TOKEN configuration.', 'error');
        } else {
          showToast(`Failed to fetch CMS pages: ${error.message || 'Unknown error'}`, 'error');
        }
        
        // Fallback to mock data in case of error
        setPages(mockCMSPages);
        showToast('Using fallback data due to API error', 'warning');
        
      } finally {
        setIsLoading(false);
      }
    };

    fetchCMSPages();
  }, [storeHandle]);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, page: CMSPage) => {
    setAnchorEl(event.currentTarget);
    setSelectedPage(page);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedPage(null);
  };



  const handleDeleteClick = (page: CMSPage) => {
    setPageToDelete(page);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleDeleteConfirm = async () => {
    if (!pageToDelete) return;
    
    setIsDeleting(true);
    
    try {
      console.log('🗑️ Deleting CMS page:', pageToDelete.id, pageToDelete.title);
      
      // Make API call to delete the page
      const response =await deleteCMSPageById(pageToDelete.id);
      
      console.log('✅ CMS page deleted successfully:', pageToDelete.id, response);
      
      // Remove page from local state
      // setPages(prev => prev.filter(page => page.id !== pageToDelete.id));
      
      // Show success message
      showToast(`Page "${pageToDelete.title}" deleted successfully!`, 'success');
      
    } catch (error: any) {
      console.error('❌ Error deleting CMS page:', error);
      
      // Check if it's an authentication error
      if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
        showToast('Authentication failed. Please check STRAPI_ACCESS_TOKEN configuration.', 'error');
      } else if (error.message?.includes('404') || error.message?.includes('Not Found')) {
        showToast('Page not found. It may have already been deleted.', 'warning');
        // Still remove from local state since it doesn't exist
        setPages(prev => prev.filter(page => page.id !== pageToDelete.id));
      } else {
        showToast(`Failed to delete page: ${error.message || 'Unknown error'}`, 'error');
        // Don't remove from local state on other errors
        return;
      }
      
    } finally {
      // Close dialog and reset state
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setPageToDelete(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusIcon = (status: CMSPage['status']) => {
    switch (status) {
      case 'published':
        return <PublicIcon fontSize="small" />;
      case 'draft':
        return <DraftIcon fontSize="small" />;
      case 'scheduled':
        return <ScheduleIcon fontSize="small" />;
      default:
        return <ArticleIcon fontSize="small" />;
    }
  };

  const getStatusColor = (status: CMSPage['status']) => {
    switch (status) {
      case 'published':
        return 'success';
      case 'draft':
        return 'warning';
      case 'scheduled':
        return 'info';
      default:
        return 'default';
    }
  };

  const stripHtml = (html: string | null | undefined) => {
    if (!html || typeof html !== 'string') {
      return '';
    }
    return html.replace(/<[^>]*>/g, '');
  };

  // Show loading state
  if (isLoading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="400px">
          <CircularProgress size={48} sx={{ mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            Loading CMS pages...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Fetching pages for {storeHandle}
          </Typography>
        </Box>
      </Container>
    );
  }
  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Page Header */}
      <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={4}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight={600}>
            CMS Pages
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage content pages for your store (About Us, Terms, Privacy Policy, etc.)
          </Typography>
        </Box>
        
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          size="large"
          onClick={() => navigateWithLoading(`/${storeHandle}/admin/cms/new`)}
          sx={{
            borderRadius: 2,
            textTransform: 'none',
            px: 3
          }}
        >
          Create Page
        </Button>
      </Box>

      {/* Stats Cards */}
      {/* <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1} sx={{ borderRadius: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Avatar sx={{ bgcolor: 'primary.main', width: 48, height: 48 }}>
                  <ArticleIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {pages.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Pages
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1} sx={{ borderRadius: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Avatar sx={{ bgcolor: 'success.main', width: 48, height: 48 }}>
                  <PublicIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {pages.filter(p => p.status === 'published').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Published
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1} sx={{ borderRadius: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Avatar sx={{ bgcolor: 'warning.main', width: 48, height: 48 }}>
                  <DraftIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {pages.filter(p => p.status === 'draft').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Drafts
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1} sx={{ borderRadius: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Avatar sx={{ bgcolor: 'info.main', width: 48, height: 48 }}>
                  <ScheduleIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {pages.filter(p => p.status === 'scheduled').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Scheduled
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid> */}

      {/* Pages Table */}
      <Card elevation={1} sx={{ borderRadius: 2 }}>
        <CardContent sx={{ p: 0 }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: 'grey.50' }}>
                  <TableCell sx={{ fontWeight: 600 }}>Page</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Content Preview</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Word Count</TableCell>
                  {/* <TableCell sx={{ fontWeight: 600 }}>Published Date</TableCell> */}
                  <TableCell sx={{ fontWeight: 600, textAlign: 'center' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {pages.map((page) => (
                  <TableRow key={page.id} hover>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={2}>
                        <Avatar
                          sx={{ 
                            bgcolor: getStatusColor(page.status) + '.main',
                            width: 40,
                            height: 40
                          }}
                        >
                          {getStatusIcon(page.status)}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2" fontWeight={600}>
                            {page.title}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            /{page.slug}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      <Box sx={{ maxWidth: 300 }}>
                        <Typography variant="body2" noWrap>
                          {stripHtml(page.content)}
                        </Typography>
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      <Chip
                        label={page.status.charAt(0).toUpperCase() + page.status.slice(1)}
                        color={getStatusColor(page.status) as any}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {page.wordCount.toLocaleString()} words
                      </Typography>
                    </TableCell>
                    
                    {/* <TableCell>
                      <Typography variant="body2">
                        {page.publishedAt ? formatDate(page.publishedAt) : '-'}
                      </Typography>
                    </TableCell> */}
                    
                    <TableCell align="center">
                      <Tooltip title="More actions">
                        <IconButton
                          onClick={(e) => handleMenuOpen(e, page)}
                          size="small"
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => {
          if (selectedPage) {
            navigateWithLoading(`/${storeHandle}/admin/cms/${selectedPage.id}`);
          }
          handleMenuClose();
        }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Page</ListItemText>
        </MenuItem>       
        <MenuItem 
          onClick={() => selectedPage && handleDeleteClick(selectedPage)}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Delete CMS Page
        </DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the page "{pageToDelete?.title}"? 
            This action cannot be undone and will remove the page from your store.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setDeleteDialogOpen(false)}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            variant="contained"
            disabled={isDeleting}
            startIcon={isDeleting ? <CircularProgress size={16} color="inherit" /> : null}
          >
            {isDeleting ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>


    </Container>
  );
};