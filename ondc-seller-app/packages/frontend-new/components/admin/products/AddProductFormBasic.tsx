'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useNavigationWithLoading } from '@/hooks/useNavigationWithLoading';
import { useProductStore } from '@/store/productStore';
import { validateProduct } from '@/lib/validations/product';
import { ImageUpload } from '@/components/ui/ImageUploadBasic';
import { LexicalContentEditor } from '@/components/ui/LexicalContentEditor';
import { ProductOptions } from './ProductOptionsBasic';
import { ProductVariants } from './ProductVariantsBasic';
import { medusaAdminService, type MedusaCategory, type MedusaCollection, type MedusaTag } from '@/lib/api/medusa-admin';

// Material Design 3 Components
import {
  Box,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Card,
  CardContent,
  Grid,
  Stack,
  <PERSON>per,
  Step,
  StepLabel,
  StepConnector,
  stepConnectorClasses,
  StepIconProps,
  Chip,
  Alert,
  CircularProgress,
  Container,
  IconButton,
  FormHelperText,
  Divider,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Publish as PublishIcon,
  NavigateNext as NavigateNextIcon,
  NavigateBefore as NavigateBeforeIcon,
  Check as CheckIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { ProductFormErrorBoundary } from './ProductFormErrorBoundary';

const steps = [
  'Basic Information',
  'Images & Media',
  'Options & Variants',
  'Content & SEO',
  'Review & Publish',
];

// Mock data as fallback
const fallbackCategories = [
  { id: 'electronics', name: 'Electronics' },
  { id: 'clothing', name: 'Clothing' },
  { id: 'home-garden', name: 'Home & Garden' },
  { id: 'sports', name: 'Sports & Outdoors' },
];

const fallbackCollections = [
  { id: 'featured', title: 'Featured Products' },
  { id: 'new-arrivals', title: 'New Arrivals' },
  { id: 'bestsellers', title: 'Best Sellers' },
  { id: 'sale', title: 'Sale Items' },
];

const fallbackTags = [
  { id: 'trending', value: 'trending' },
  { id: 'eco-friendly', value: 'eco-friendly' },
  { id: 'premium', value: 'premium' },
  { id: 'limited-edition', value: 'limited-edition' },
  { id: 'bestseller', value: 'bestseller' },
  { id: 'new', value: 'new' },
  { id: 'sale', value: 'sale' },
  { id: 'featured', value: 'featured' },
];

// Custom Step Connector
const CustomConnector = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 22,
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundColor: theme.palette.primary.main,
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundColor: theme.palette.success.main,
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    height: 3,
    border: 0,
    backgroundColor: theme.palette.grey[300],
    borderRadius: 1,
  },
}));

// Custom Step Icon
const CustomStepIcon = (props: StepIconProps) => {
  const { active, completed, className } = props;

  return (
    <Box
      className={className}
      sx={{
        width: 32,
        height: 32,
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '0.875rem',
        fontWeight: 'medium',
        bgcolor: completed
          ? 'success.main'
          : active
          ? 'primary.main'
          : 'grey.300',
        color: completed || active ? 'white' : 'grey.600',
      }}
    >
      {completed ? (
        <CheckIcon sx={{ fontSize: 18 }} />
      ) : (
        props.icon
      )}
    </Box>
  );
};

export const AddProductForm: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const { navigateToProducts } = useNavigationWithLoading();

  // State for API data
  const [categories, setCategories] = useState<MedusaCategory[]>([]);
  const [collections, setCollections] = useState<MedusaCollection[]>([]);
  const [tags, setTags] = useState<MedusaTag[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [dataError, setDataError] = useState<string | null>(null);
  const [maxHeight, setMaxHeight] = React.useState(100);

  const {
    formData,
    currentStep,
    isLoading,
    errors,
    setCurrentStep,
    updateField,
    addOption,
    updateOption,
    removeOption,
    addVariant,
    updateVariant,
    removeVariant,
    generateVariantsFromOptions,
    addImage,
    removeImage,
    setThumbnail,
    saveProduct,
    resetForm,
    setErrors,
  } = useProductStore();

  // Reset form when component mounts
  useEffect(() => {
    try {
      resetForm();
    } catch (error) {
      console.error('Error resetting form on mount:', error);
    }
  }, []); // Remove resetForm dependency to prevent infinite loops

  // Fetch categories, collections, and tags from Medusa API
  useEffect(() => {
    const fetchData = async () => {
      setIsLoadingData(true);
      setDataError(null);
      
      try {
        console.log('Fetching data from Medusa API...');
        
        // Fetch all data in parallel
        const [categoriesData, collectionsData, tagsData] = await Promise.allSettled([
          medusaAdminService.getCategories(storeHandle),
          medusaAdminService.getCollections(storeHandle),
          medusaAdminService.getTags(storeHandle),
        ]);
        
        // Handle categories
        if (categoriesData.status === 'fulfilled') {
          setCategories(categoriesData.value);
          console.log('Categories loaded:', categoriesData.value.length);
        } else {
          console.error('Failed to load categories:', categoriesData.reason);
          setCategories(fallbackCategories as any);
        }
        
        // Handle collections
        if (collectionsData.status === 'fulfilled') {
          setCollections(collectionsData.value);
          console.log('Collections loaded:', collectionsData.value.length);
        } else {
          console.error('Failed to load collections:', collectionsData.reason);
          setCollections(fallbackCollections as any);
        }
        
        // Handle tags
        if (tagsData.status === 'fulfilled') {
          setTags(tagsData.value);
          console.log('Tags loaded:', tagsData.value.length);
        } else {
          console.error('Failed to load tags:', tagsData.reason);
          setTags(fallbackTags as any);
        }
        
      } catch (error) {
        console.error('Error fetching data:', error);
        setDataError('Failed to load data from server. Using fallback data.');
        // Use fallback data
        setCategories(fallbackCategories as any);
        setCollections(fallbackCollections as any);
        setTags(fallbackTags as any);
      } finally {
        setIsLoadingData(false);
      }
    };
    
    fetchData();
  }, [storeHandle]);

  // Validation functions for each step - memoized to prevent infinite re-renders
  const validateCurrentStep = React.useCallback(() => {
    const errors: Record<string, string> = {};
    
    try {
      switch (currentStep) {
        case 0: // Basic Information
          if (!formData.productName?.trim()) {
            errors.productName = 'Product name is required';
          }
          if (!formData.productHandle?.trim()) {
            errors.productHandle = 'Product handle is required';
          }
          if (!formData.productDescription?.trim()) {
            errors.productDescription = 'Product description is required';
          }
          if (!formData.productCategory?.trim()) {
            errors.productCategory = 'Product category is required';
          }
          break;
          
        case 1: // Images & Media
          if (!formData.productImages || formData.productImages.length === 0) {
            errors.productImages = 'At least one product image is required';
          }
          break;
          
        case 2: // Options & Variants
          if (!formData.productVariants || formData.productVariants.length === 0) {
            errors.productVariants = 'At least one product variant is required';
          }
          break;
          
        case 3: // Content & SEO
          if (!formData.productOverview?.trim()) {
            errors.productOverview = 'Product overview is required';
          }
          if (!formData.productFeatures?.trim()) {
            errors.productFeatures = 'Product features are required';
          }
          if (!formData.productSpecifications?.trim()) {
            errors.productSpecifications = 'Product specifications are required';
          }
          break;
      }
      
      return Object.keys(errors).length === 0;
    } catch (error) {
      console.error('Error in validateCurrentStep:', error);
      return false;
    }
  }, [currentStep, formData.productName, formData.productHandle, formData.productDescription, formData.productCategory, formData.productImages, formData.productVariants, formData.productOverview, formData.productFeatures, formData.productSpecifications]);

  // Check if current step is valid without setting errors
  const isCurrentStepValid = React.useMemo(() => {
    return validateCurrentStep();
  }, [validateCurrentStep]);

  const handleNext = () => {
    try {
      // Validate and set errors for display
      const errors: Record<string, string> = {};
      
      switch (currentStep) {
        case 0: // Basic Information
          if (!formData.productName?.trim()) {
            errors.productName = 'Product name is required';
          }
          if (!formData.productHandle?.trim()) {
            errors.productHandle = 'Product handle is required';
          }
          if (!formData.productDescription?.trim()) {
            errors.productDescription = 'Product description is required';
          }
          if (!formData.productCategory?.trim()) {
            errors.productCategory = 'Product category is required';
          }
          break;
          
        case 1: // Images & Media
          if (!formData.productImages || formData.productImages.length === 0) {
            errors.productImages = 'At least one product image is required';
          }
          break;
          
        case 2: // Options & Variants
          if (!formData.productVariants || formData.productVariants.length === 0) {
            errors.productVariants = 'At least one product variant is required';
          }
          break;
          
        case 3: // Content & SEO
          if (!formData.productOverview?.trim()) {
            errors.productOverview = 'Product overview is required';
          }
          if (!formData.productFeatures?.trim()) {
            errors.productFeatures = 'Product features are required';
          }
          if (!formData.productSpecifications?.trim()) {
            errors.productSpecifications = 'Product specifications are required';
          }
          break;
      }
      
      setErrors(errors);
      
      if (Object.keys(errors).length === 0 && currentStep < steps.length - 1) {
        setCurrentStep(currentStep + 1);
      }
    } catch (error) {
      console.error('Error in handleNext:', error);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const validateAllRequiredFields = React.useCallback(() => {
    const errors: Record<string, string> = {};
    
    try {
      // Basic Information
      if (!formData.productName?.trim()) {
        errors.productName = 'Product name is required';
      }
      if (!formData.productHandle?.trim()) {
        errors.productHandle = 'Product handle is required';
      }
      if (!formData.productDescription?.trim()) {
        errors.productDescription = 'Product description is required';
      }
      if (!formData.productCategory?.trim()) {
        errors.productCategory = 'Product category is required';
      }
      
      // Images
      if (!formData.productImages || formData.productImages.length === 0) {
        errors.productImages = 'At least one product image is required';
      }
      
      // Variants
      if (!formData.productVariants || formData.productVariants.length === 0) {
        errors.productVariants = 'At least one product variant is required';
      }
      
      // Content
      if (!formData.productOverview?.trim()) {
        errors.productOverview = 'Product overview is required';
      }
      if (!formData.productFeatures?.trim()) {
        errors.productFeatures = 'Product features are required';
      }
      if (!formData.productSpecifications?.trim()) {
        errors.productSpecifications = 'Product specifications are required';
      }
      
      setErrors(errors);
      return Object.keys(errors).length === 0;
    } catch (error) {
      console.error('Error in validateAllRequiredFields:', error);
      setErrors({ general: 'Validation error occurred' });
      return false;
    }
  }, [formData, setErrors]);

  const handleSave = async (isDraft = true) => {
    try {
      // Validate all required fields before saving
      if (!validateAllRequiredFields()) {
        // Find the first step with errors and navigate to it
        const errorFields = Object.keys(errors);
        if (errorFields.some(field => ['productName', 'productHandle', 'productDescription', 'productCategory'].includes(field))) {
          setCurrentStep(0);
        } else if (errorFields.includes('productImages')) {
          setCurrentStep(1);
        } else if (errorFields.includes('productVariants')) {
          setCurrentStep(2);
        } else if (errorFields.some(field => ['productOverview', 'productFeatures', 'productSpecifications'].includes(field))) {
          setCurrentStep(3);
        }
        return;
      }

      // Additional validation using existing validateProduct function
      const validation = validateProduct({
        ...formData,
        status: isDraft ? 'draft' : 'active',
      });

      if (!validation.success) {
        const fieldErrors: Record<string, string> = {};
        validation.error.errors.forEach((error) => {
          const field = error.path.join('.');
          fieldErrors[field] = error.message;
        });
        setErrors(fieldErrors);
        return;
      }

      const success = await saveProduct();
      if (success) {
        navigateToProducts(storeHandle);
      }
    } catch (error) {
      console.error('Error in handleSave:', error);
      setErrors({ general: 'An error occurred while saving the product' });
    }
  };

  const handleBackToProducts = () => {
    resetForm();
    navigateToProducts(storeHandle);
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const newTags = (formData.productTags || []).filter(tag => tag !== tagToRemove);
    updateField('productTags', newTags);
  };

  const handleAddTag = (tagValue: string) => {
    if (tagValue && !(formData.productTags || []).includes(tagValue)) {
      updateField('productTags', [...(formData.productTags || []), tagValue]);
    }
  };

  const handleOpen = (event) => {
    // Calculate the space below the anchor element (the select input)
    const anchorRect = event.currentTarget.getBoundingClientRect();
    const availableHeight = window.innerHeight - anchorRect.bottom - 16; // 16px for padding
    setMaxHeight(Math.max(availableHeight, 115)); // 115 as minimum
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Stack spacing={3}>
            <Grid container spacing={3}>
              <Grid item size={{xs:12, md:6}}>
                <TextField
                  fullWidth
                  label="Product Name"
                  value={formData.productName || ''}
                  onChange={(e) => updateField('productName', e.target.value)}
                  error={!!errors.productName}
                  helperText={errors.productName}
                  required
                />
              </Grid>
              
              <Grid item size={{xs:12, md:6}}>
                <TextField
                  fullWidth
                  label="Product Handle"
                  value={formData.productHandle || ''}
                  onChange={(e) => updateField('productHandle', e.target.value)}
                  error={!!errors.productHandle}
                  helperText={errors.productHandle || 'URL-friendly version of product name'}
                  required
                />
              </Grid>
              
              <Grid item size={{xs:12, md:12}}>
                <TextField
                  fullWidth
                  label="Product Subtitle"
                  value={formData.productSubtitle || ''}
                  onChange={(e) => updateField('productSubtitle', e.target.value)}
                  helperText="Short description that appears below the product name"
                />
              </Grid>
              
              <Grid item size={12}>
                <TextField
                  fullWidth
                  label="Product Description"
                  multiline
                  rows={4}
                  value={formData.productDescription || ''}
                  onChange={(e) => updateField('productDescription', e.target.value)}
                  error={!!errors.productDescription}
                  helperText={errors.productDescription}
                  required
                />
              </Grid>
              
              <Grid item size={{xs:12, md:6}}>
                <FormControl fullWidth error={!!errors.productCategory} required>
                  <InputLabel>Product Category</InputLabel>
                  <Select
                    value={formData.productCategory || ''}
                    label="Product Category"
                    onChange={(e) => updateField('productCategory', e.target.value)}
                    disabled={isLoadingData}
                    onOpen={handleOpen}
                    MenuProps={{
                      anchorOrigin: { vertical: 'bottom', horizontal: 'left' },
                      transformOrigin: { vertical: 'top', horizontal: 'left' },
                      PaperProps: { style: { maxHeight: maxHeight } },
                    }}
                  >
                    {categories.map((category) => (
                      <MenuItem key={category.id} value={category.id}>
                        {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.productCategory && (
                    <FormHelperText>{errors.productCategory}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              
              <Grid item size={{xs:12, md:6}}>
                <FormControl fullWidth>
                  <InputLabel>Product Collection</InputLabel>
                  <Select
                    value={formData.productCollection || ''}
                    label="Product Collection"
                    onChange={(e) => updateField('productCollection', e.target.value)}
                    disabled={isLoadingData}
                    onOpen={handleOpen}
                    MenuProps={{
                      anchorOrigin: { vertical: 'bottom', horizontal: 'left' },
                      transformOrigin: { vertical: 'top', horizontal: 'left' },
                      PaperProps: { style: { maxHeight: maxHeight } },
                    }}
                  >
                    <MenuItem value="">None</MenuItem>
                    {collections.map((collection) => (
                      <MenuItem key={collection.id} value={collection.id}>
                        {collection.title}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item size={12}>
                <Grid size={{xs:12, md:12}}>
                  <Box >
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Product Tags
                    </Typography>
                    <Stack direction="row" spacing={1} sx={{ mb: 2, flexWrap: 'wrap', gap: 1 }}>
                      {(formData.productTags || []).map((tag, index) => (
                        <Chip
                          key={index}
                          label={tag}
                          onDelete={() => handleRemoveTag(tag)}
                          color="primary"
                          variant="outlined"
                        />
                      ))}
                    </Stack>
                    <FormControl fullWidth>
                      <InputLabel>Add Tags</InputLabel>
                      <Select
                        value=""
                        label="Add Tags"
                        onChange={(e) => {
                          handleAddTag(e.target.value);
                        }}
                        disabled={isLoadingData}
                        onOpen={handleOpen}
                        MenuProps={{
                          anchorOrigin: { vertical: 'bottom', horizontal: 'left' },
                          transformOrigin: { vertical: 'top', horizontal: 'left' },
                          PaperProps: { style: { maxHeight: maxHeight } },
                        }}
                      >
                        {tags.map((tag) => (
                          <MenuItem key={tag.id} value={tag.value}>
                            {tag.value}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>
                </Grid>
              </Grid>
            </Grid>
          </Stack>
        );

      case 1:
        return (
          <Box>
            <ImageUpload
              images={formData.productImages || []}
              onImagesChange={(images) => updateField('productImages', images)}
              thumbnailId={formData.productThumbnail?.id}
              onThumbnailChange={(imageId) => {
                const image = formData.productImages?.find(img => img.id === imageId);
                if (image) {
                  setThumbnail(image);
                }
              }}
              label="Product Images *"
              error={errors.productImages}
              helperText="Upload high-quality images of your product. The first image will be used as the thumbnail."
            />
          </Box>
        );

      case 2:
        return (
          <Stack spacing={4}>
            <ProductOptions
              options={formData.productOptions || []}
              onChange={(options) => updateField('productOptions', options)}
              onGenerateVariants={generateVariantsFromOptions}
              error={errors.productOptions}
            />
            <Divider />
            <ProductVariants
              variants={formData.productVariants || []}
              onChange={(variants) => updateField('productVariants', variants)}
              error={errors.productVariants}
            />
          </Stack>
        );

      case 3:
        return (
          <Stack spacing={4}>
            <LexicalContentEditor
              label="Product Overview"
              value={formData.productOverview || ''}
              onChange={(value) => updateField('productOverview', value)}
              placeholder="Provide a detailed overview of your product. Describe what makes it unique, its main purpose, and why customers should choose it..."
              height={250}
              error={errors.productOverview}
              helperText="Write a compelling overview that highlights your product's value proposition"
              required
            />
            <LexicalContentEditor
              label="Product Features"
              value={formData.productFeatures || ''}
              onChange={(value) => updateField('productFeatures', value)}
              placeholder="List the key features and benefits of your product. Use bullet points or numbered lists for better readability..."
              height={250}
              error={errors.productFeatures}
              helperText="Highlight the most important features that set your product apart"
              required
            />
            <LexicalContentEditor
              label="Product Specifications"
              value={formData.productSpecifications || ''}
              onChange={(value) => updateField('productSpecifications', value)}
              placeholder="Include technical specifications, dimensions, materials, compatibility, warranty information, etc..."
              height={250}
              error={errors.productSpecifications}
              helperText="Provide detailed technical information that customers need to make informed decisions"
              required
            />
          </Stack>
        );

      case 4:
        return (
          <Stack spacing={3}>
            <Alert severity="info" icon={<InfoIcon />}>
              Review all product information before publishing. You can save as draft to continue editing later.
            </Alert>
            
            <Grid container spacing={3}>
              <Grid item size={{xs:12, md:6}}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Basic Information
                    </Typography>
                    <Stack spacing={1}>
                      <Box display='flex'>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }} color="text.secondary">Name:</Typography>
                        <Typography variant="body2">{formData.productName || 'Not set'}</Typography>
                      </Box>
                      <Box  display='flex'>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }} color="text.secondary">Handle:</Typography>
                        <Typography variant="body2">{formData.productHandle || 'Not set'}</Typography>
                      </Box>
                      <Box display='flex'>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }} color="text.secondary">Category:</Typography>
                        <Typography variant="body2">
                          {categories.find(c => c.id === formData.productCategory)?.name || 'Not selected'}
                        </Typography>
                      </Box>
                      <Box display='flex'>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }} color="text.secondary">Collection:</Typography>
                        <Typography variant="body2">
                          {collections.find(c => c.id === formData.productCollection)?.title || 'None'}
                        </Typography>
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item size={{xs:12, md:6}}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Pricing & Inventory
                    </Typography>
                    <Stack spacing={1}>
                      <Box display='flex'>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }} color="text.secondary">Variants:</Typography>
                        <Typography variant="body2">{formData.productVariants?.length || 0}</Typography>
                      </Box>
                      <Box display='flex'>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }} color="text.secondary">Starting Price:</Typography>
                        <Typography variant="body2">₹{formData.productSalePrice || 0}</Typography>
                      </Box>
                      <Box display='flex'>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }} color="text.secondary">Total Stock:</Typography>
                        <Typography variant="body2">{formData.productStock || 0}</Typography>
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item size={{xs:12, md:6}}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Images
                    </Typography>
                    <Stack spacing={1}>
                      <Box display='flex'>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }} color="text.secondary">Product Images:</Typography>
                        <Typography variant="body2">{formData.productImages?.length || 0} uploaded</Typography>
                      </Box>
                      <Box display='flex'>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }} color="text.secondary">Thumbnail:</Typography>
                        <Typography variant="body2">{formData.productThumbnail ? 'Set' : 'Not set'}</Typography>
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Stack>
        );

      default:
        return null;
    }
  };

  return (
    <ProductFormErrorBoundary>
      <Container sx={{ py: 3 }} id='#main-container-mui'>
      {/* Header */}
      <Stack spacing={3} sx={{ mb: 4 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={handleBackToProducts}
          sx={{ alignSelf: 'flex-start' }}
        >
          Back to Products
        </Button>
        
        <Box>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Add New Product
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Create a new product for your store with detailed information, images, and variants.
          </Typography>
        </Box>
        
        {/* Data loading status */}
        {isLoadingData && (
          <Alert severity="info" icon={<CircularProgress size={20} />}>
            Loading categories, collections, and tags from Medusa...
          </Alert>
        )}
        
        {/* Data error status */}
        {dataError && (
          <Alert severity="warning">
            {dataError}
          </Alert>
        )}
      </Stack>

      {/* Stepper */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Stepper
            activeStep={currentStep}
            connector={<CustomConnector />}
            alternativeLabel
          >
            {steps.map((label, index) => (
              <Step key={label}>
                <StepLabel StepIconComponent={CustomStepIcon}>
                  <Typography variant="body2">{label}</Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </CardContent>
      </Card>

      {/* Form Content */}
      <Card sx={{ mb: 4 }}>
        <CardContent sx={{ p: 4 }}>
          <Typography variant="h5" gutterBottom>
            {steps[currentStep]}
          </Typography>
          
          {/* Validation Errors Alert */}
          {Object.keys(errors).length > 0 && (
            <Alert severity="error" sx={{ mb: 3 }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                Please fix the following errors:
              </Typography>
              <ul style={{ margin: 0, paddingLeft: '20px' }}>
                {Object.entries(errors).map(([field, message]) => (
                  <li key={field}>
                    <Typography variant="body2">{message}</Typography>
                  </li>
                ))}
              </ul>
            </Alert>
          )}
          
          {renderStepContent(currentStep)}
        </CardContent>
      </Card>

      {/* Navigation */}
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Button
          startIcon={<NavigateBeforeIcon />}
          onClick={handleBack}
          disabled={currentStep === 0}
          variant="outlined"
        >
          Back
        </Button>

        <Stack direction="row" spacing={2}>
          {currentStep === steps.length - 1 ? (
            <>
              <Button
                startIcon={isLoading ? <CircularProgress size={20} /> : <SaveIcon />}
                onClick={() => handleSave(true)}
                disabled={isLoading}
                variant="outlined"
              >
                Save as Draft
              </Button>
              <Button
                startIcon={isLoading ? <CircularProgress size={20} /> : <PublishIcon />}
                onClick={() => handleSave(false)}
                disabled={isLoading}
                variant="contained"
              >
                Publish Product
              </Button>
            </>
          ) : (
            <Button
              endIcon={<NavigateNextIcon />}
              onClick={handleNext}
              variant="contained"
              disabled={!isCurrentStepValid}
            >
              Next
            </Button>
          )}
        </Stack>
      </Stack>
      </Container>
    </ProductFormErrorBoundary>
  );
};