'use client';

import React, { useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Box,
  Container,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Button,
  Paper,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Autocomplete,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material';
import {
  ArrowBack,
  Save,
  Preview,
  NavigateNext,
  NavigateBefore,
} from '@mui/icons-material';
import { useProductStore } from '@/store/productStore';
import { ImageUpload } from '@/components/ui/ImageUploadBasic';
import { RichTextEditor } from '@/components/ui/RichTextEditorBasic';
import { ProductOptions } from './ProductOptions';
import { ProductVariants } from './ProductVariants';
import { useToast } from '@/app/providers/toast-provider';
import { medusaAdminService } from '@/lib/api/medusa-admin';
import { TestComponent } from './TestComponent';

const steps = [
  'Basic Information',
  'Images & Media',
  'Options & Variants',
  'Content & SEO',
  'Review & Publish',
];

// Mock data for dropdowns
const mockCategories = [
  { id: 'electronics', name: 'Electronics' },
  { id: 'clothing', name: 'Clothing' },
  { id: 'home-garden', name: 'Home & Garden' },
  { id: 'sports', name: 'Sports & Outdoors' },
];

const mockCollections = [
  { id: 'featured', name: 'Featured Products' },
  { id: 'new-arrivals', name: 'New Arrivals' },
  { id: 'bestsellers', name: 'Best Sellers' },
  { id: 'sale', name: 'Sale Items' },
];

const mockTags = [
  'trending',
  'eco-friendly',
  'premium',
  'limited-edition',
  'bestseller',
  'new',
  'sale',
  'featured',
];

export const AddProductForm: React.FC = () => {
  try {
    console.log('=== COMPONENT START ===');
    
    const router = useRouter();
    const params = useParams();
    const storeHandle = params.storeHandle as string;
    const { showToast } = useToast();
    
    // Debug store handle
    console.log('=== COMPONENT INIT ===');
    console.log('params:', params);
    console.log('storeHandle:', storeHandle);
    console.log('router:', router);
  const [categories, setCategories] = React.useState<any[]>([]);
  const [collections, setCollections] = React.useState<any[]>([]);
  const [tags, setTags] = React.useState<any[]>([]);
  const [isLoadingData, setIsLoadingData] = React.useState(false);

  const {
    formData,
    currentStep,
    isLoading,
    errors,
    setCurrentStep,
    updateField,
    addOption,
    updateOption,
    removeOption,
    addVariant,
    updateVariant,
    removeVariant,
    generateVariantsFromOptions,
    addImage,
    removeImage,
    setThumbnail,
    saveProduct,
    resetForm,
    setErrors,
  } = useProductStore();

  // Reset form when component mounts
  useEffect(() => {
    resetForm();
  }, [resetForm]);

  // Load categories, collections, and tags
  useEffect(() => {
    const loadData = async () => {
      setIsLoadingData(true);
      try {
        const [categoriesData, collectionsData, tagsData] = await Promise.all([
          medusaAdminService.getCategories(storeHandle),
          medusaAdminService.getCollections(storeHandle),
          medusaAdminService.getTags(storeHandle)
        ]);
        
        setCategories(categoriesData || []);
        setCollections(collectionsData || []);
        setTags(tagsData || []);
      } catch (error) {
        console.error('Error loading form data:', error);
        showToast('Failed to load form data', 'error');
      } finally {
        setIsLoadingData(false);
      }
    };
    
    if (storeHandle) {
      loadData();
    }
  }, [storeHandle, showToast]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSave = async (isDraft = true) => {
    console.log('=== HANDLE SAVE CALLED ===');
    console.log('isDraft:', isDraft);
    console.log('formData:', formData);
    alert(`handleSave called with isDraft: ${isDraft}`);

    try {
      // Pass the status directly to saveProduct
      const status = isDraft ? 'draft' : 'active';
      console.log('Calling saveProduct with status:', status);
      alert(`About to call saveProduct with status: ${status}`);
      
      const success = await saveProduct(status);
      console.log('Save result:', success);
      alert(`saveProduct returned: ${success}`);
      
      if (success) {
        alert('Success! Showing toast and redirecting...');
        showToast(
          isDraft ? 'Product saved as draft successfully' : 'Product published successfully', 
          'success'
        );
        router.push(`/${storeHandle}/admin/products`);
      } else {
        alert('Failed! Showing error toast...');
        showToast('Failed to save product', 'error');
      }
    } catch (error) {
      console.error('Error saving product:', error);
      alert(`Error caught: ${error}`);
      showToast('Failed to save product', 'error');
    }
  };

  const handleBackToProducts = () => {
    resetForm();
    router.push(`/${storeHandle}/admin/products`);
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item size={{xs:12, sm:6 }}>
              <TextField
                fullWidth
                label="Product Name"
                value={formData.productName || ''}
                onChange={(e) => updateField('productName', e.target.value)}
                helperText="Enter the product name"
              />
            </Grid>
            <Grid item size={{xs:12, sm:6 }}>
              <TextField
                fullWidth
                label="Product Handle"
                value={formData.productHandle || ''}
                onChange={(e) => updateField('productHandle', e.target.value)}
                helperText="URL-friendly version of product name"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Product Subtitle"
                value={formData.productSubtitle || ''}
                onChange={(e) => updateField('productSubtitle', e.target.value)}
                helperText="Short description that appears below the product name"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Product Description"
                value={formData.productDescription || ''}
                onChange={(e) => updateField('productDescription', e.target.value)}
                helperText="Enter product description"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Product Category</InputLabel>
                <Select
                  value={formData.productCategory || ''}
                  label="Product Category"
                  onChange={(e) => updateField('productCategory', e.target.value)}
                  disabled={isLoadingData}
                >
                  {categories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Product Collection</InputLabel>
                <Select
                  value={formData.productCollection || ''}
                  label="Product Collection"
                  onChange={(e) => updateField('productCollection', e.target.value)}
                  disabled={isLoadingData}
                >
                  <MenuItem value="">None</MenuItem>
                  {collections.map((collection) => (
                    <MenuItem key={collection.id} value={collection.id}>
                      {collection.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Autocomplete
                multiple
                options={tags.map(tag => tag.value)}
                value={formData.productTags || []}
                onChange={(_, newValue) => updateField('productTags', newValue)}
                disabled={isLoadingData}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip variant="outlined" label={option} {...getTagProps({ index })} key={option} />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Product Tags"
                    placeholder={isLoadingData ? "Loading tags..." : "Select or type tags"}
                  />
                )}
              />
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <ImageUpload
                images={formData.productImages || []}
                onImagesChange={(images) => updateField('productImages', images)}
                thumbnailId={formData.productThumbnail?.id}
                onThumbnailChange={(imageId) => {
                  const image = formData.productImages?.find(img => img.id === imageId);
                  if (image) {
                    setThumbnail(image);
                  }
                }}
                label="Product Images"
                helperText="Upload high-quality images of your product. The first image will be used as the thumbnail."
              />
            </Grid>
          </Grid>
        );

      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <ProductOptions
                options={formData.productOptions || []}
                onChange={(options) => updateField('productOptions', options)}
                onGenerateVariants={generateVariantsFromOptions}
                error={errors.productOptions}
              />
            </Grid>
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
            </Grid>
            <Grid item xs={12}>
              <ProductVariants
                variants={formData.productVariants || []}
                onChange={(variants) => updateField('productVariants', variants)}
                error={errors.productVariants}
              />
            </Grid>
          </Grid>
        );

      case 3:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <RichTextEditor
                label="Product Overview"
                value={formData.productOverview || ''}
                onChange={(value) => updateField('productOverview', value)}
                placeholder="Provide a detailed overview of your product..."
                height={200}
              />
            </Grid>
            <Grid item xs={12}>
              <RichTextEditor
                label="Product Features"
                value={formData.productFeatures || ''}
                onChange={(value) => updateField('productFeatures', value)}
                placeholder="List the key features and benefits..."
                height={200}
              />
            </Grid>
            <Grid item xs={12}>
              <RichTextEditor
                label="Product Specifications"
                value={formData.productSpecifications || ''}
                onChange={(value) => updateField('productSpecifications', value)}
                placeholder="Include technical specifications, dimensions, materials, etc..."
                height={200}
              />
            </Grid>
          </Grid>
        );

      case 4:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Alert severity="info" sx={{ mb: 3 }}>
                Review all product information before publishing. You can save as draft to continue editing later.
              </Alert>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Basic Information
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Name:</strong> {formData.productName}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Handle:</strong> {formData.productHandle}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Category:</strong> {categories.find(c => c.id === formData.productCategory)?.name || 'None'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Collection:</strong> {collections.find(c => c.id === formData.productCollection)?.title || 'None'}
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Pricing & Inventory
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Variants:</strong> {formData.productVariants?.length || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Starting Price:</strong> ₹{formData.productSalePrice || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Total Stock:</strong> {formData.productStock || 0}
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Images
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Product Images:</strong> {formData.productImages?.length || 0} uploaded
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Thumbnail:</strong> {formData.productThumbnail ? 'Set' : 'Not set'}
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        );

      default:
        return null;
    }
  };



  // Add a simple test at the top level
  console.log('Component is rendering...');
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* MINIMAL TEST COMPONENT */}
      <TestComponent />
      
      {/* EMERGENCY TEST BUTTON - VERY SIMPLE */}
      <div style={{ padding: '20px', backgroundColor: 'red', color: 'white', marginBottom: '20px' }}>
        <h2>EMERGENCY TEST SECTION</h2>
        <button 
          onClick={() => {
            alert('EMERGENCY BUTTON WORKS!');
            console.log('EMERGENCY BUTTON CLICKED');
          }}
          style={{ padding: '10px', fontSize: '16px', backgroundColor: 'yellow', color: 'black' }}
        >
          EMERGENCY TEST BUTTON
        </button>
        <br /><br />
        <button 
          onClick={() => {
            alert('PUBLISH TEST WORKS!');
            console.log('PUBLISH TEST CLICKED');
            // Direct function call
            handleSave(false);
          }}
          style={{ padding: '10px', fontSize: '16px', backgroundColor: 'green', color: 'white' }}
        >
          PUBLISH TEST BUTTON
        </button>
      </div>
      
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Button
          startIcon={<ArrowBack />}
          onClick={handleBackToProducts}
          sx={{ mb: 2 }}
        >
          Back to Products
        </Button>
        <Typography variant="h4" component="h1" gutterBottom>
          Add New Product
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Create a new product for your store with detailed information, images, and variants.
        </Typography>
      </Box>

      {/* Stepper */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Stepper activeStep={currentStep} alternativeLabel>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Paper>

      {/* Form Content */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          {steps[currentStep]}
        </Typography>
        {renderStepContent(currentStep)}
      </Paper>



      {/* Navigation */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Button
          onClick={handleBack}
          disabled={currentStep === 0}
          startIcon={<NavigateBefore />}
        >
          Back
        </Button>

        <Box sx={{ display: 'flex', gap: 2 }}>
          {/* Test API call button - always visible */}
          <Button
            variant="contained"
            color="secondary"
            onClick={async () => {
              console.log('=== DIRECT API TEST ===');
              try {
                const testPayload = {
                  title: 'Test Product Direct',
                  handle: 'test-product-direct',
                  description: 'Direct API test',
                  status: 'draft',
                  categories: [],
                  collection_id: '',
                  tags: [],
                  options: [],
                  metadata: {
                    additional_data: {
                      product_prices: [{ sale_price: 100, original_price: 120 }],
                      product_quantity: 10,
                      product_inventory_status: 'in_stock',
                      product_overview: 'Test overview',
                      product_features: 'Test features',
                      product_specifications: 'Test specs'
                    }
                  },
                  variants: [{
                    title: 'default',
                    sku: 'test-sku-001',
                    material: null,
                    weight: null,
                    width: null,
                    length: null,
                    height: null,
                    metadata: {
                      sale_price: 100,
                      original_price: 120,
                      product_quantity: 10,
                      product_inventory_status: 'in_stock'
                    },
                    prices: [{ currency_code: 'inr', amount: 120 }]
                  }],
                  images: [],
                  thumbnail: ''
                };
                
                console.log('Direct API call with payload:', testPayload);
                const result = await medusaAdminService.createProduct(storeHandle, testPayload);
                console.log('Direct API result:', result);
                alert('Direct API call successful! Check console.');
              } catch (error) {
                console.error('Direct API call failed:', error);
                alert('Direct API call failed! Check console.');
              }
            }}
          >
            TEST API DIRECT
          </Button>
          
          {/* Always show publish buttons for testing */}
          <Button
            variant="outlined"
            onClick={() => {
              console.log('Draft button clicked - calling handleSave(true)');
              alert('Draft button clicked!');
              handleSave(true);
            }}
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} /> : <Save />}
          >
            Save as Draft (ALWAYS VISIBLE)
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              console.log('Publish button clicked - calling handleSave(false)');
              alert('Publish button clicked!');
              handleSave(false);
            }}
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} /> : <Preview />}
          >
            Publish Product (ALWAYS VISIBLE)
          </Button>
          
          {/* Original step navigation */}
          {currentStep < steps.length - 1 && (
            <Button
              variant="contained"
              onClick={handleNext}
              endIcon={<NavigateNext />}
            >
              Next
            </Button>
          )}
        </Box>
      </Box>
    </Container>
  );
  
  } catch (error) {
    console.error('=== COMPONENT ERROR ===', error);
    return (
      <div style={{ padding: '20px', backgroundColor: 'red', color: 'white' }}>
        <h1>COMPONENT ERROR!</h1>
        <p>Error: {String(error)}</p>
        <button onClick={() => alert('Error button works!')}>
          Test Button in Error State
        </button>
      </div>
    );
  }
};