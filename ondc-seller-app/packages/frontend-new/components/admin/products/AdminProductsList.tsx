'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { useNavigationWithLoading } from '@/hooks/useNavigationWithLoading';
import { AdminTableLoading } from '../AdminLoading';
import { useAuthStore } from '@/stores/authStore';
import { medusaAdminService, type MedusaProduct } from '@/lib/api/medusa-admin';
import { useToast } from '@/app/providers/toast-provider';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';
import { useDebounce } from '@/hooks/useDebounce';
import { BulkImportModal } from './BulkImportModal';

// Material Design 3 Components
import {
  Box,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Chip,
  IconButton,
  Paper,
  Alert,
  CircularProgress,
  Stack,
  Divider,
  InputAdornment,
  Fab,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FileUpload as ImportIcon,
  Inventory as ProductIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';

export const AdminProductsList: React.FC = () => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const { getStoreHandle } = useAuthStore();
  const { showToast } = useToast();
  const { navigateToAddProduct, navigateToEditProduct } = useNavigationWithLoading();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [products, setProducts] = useState<MedusaProduct[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [deletingProductId, setDeletingProductId] = useState<string | null>(null);
  const [deleteConfirmModal, setDeleteConfirmModal] = useState<{
    isOpen: boolean;
    productId: string;
    productName: string;
  }>({ isOpen: false, productId: '', productName: '' });
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  
  // Get dynamic store handle from auth store
  const dynamicStoreHandle = getStoreHandle() || storeHandle;
  
  // Debounce search term to prevent excessive API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  
  // Fetch products from API
  const fetchProducts = useCallback(async () => {
    if (!dynamicStoreHandle) {
      setIsLoading(false);
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const productsArray = await medusaAdminService.getProducts(dynamicStoreHandle);
      
      // Filter products by search term and status if provided
      let filteredProducts = productsArray;
      if (debouncedSearchTerm) {
        filteredProducts = productsArray.filter(product => 
          product.title.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
          (product.handle && product.handle.toLowerCase().includes(debouncedSearchTerm.toLowerCase()))
        );
      }
      
      if (statusFilter !== 'all') {
        filteredProducts = filteredProducts.filter(product => product.status === statusFilter);
      }
      
      setProducts(filteredProducts);
      setTotalCount(productsArray.length);
    } catch (error: any) {
      console.error('Failed to fetch products:', error);
      setError(error.message || 'Failed to fetch products');
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  }, [dynamicStoreHandle, debouncedSearchTerm, statusFilter]);

  useEffect(() => {
    fetchProducts();
  }, [dynamicStoreHandle, debouncedSearchTerm, statusFilter]);

  // Products are already filtered by the API based on search and status
  const filteredProducts = products;

  const handleSelectAll = () => {
    if (selectedProducts.length === filteredProducts.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(filteredProducts.map(p => p.id));
    }
  };

  const handleSelectProduct = (productId: string) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleDeleteClick = (productId: string, productName: string) => {
    setDeleteConfirmModal({
      isOpen: true,
      productId,
      productName
    });
  };

  const handleDeleteConfirm = async () => {
    const { productId } = deleteConfirmModal;
    
    setDeletingProductId(productId);
    try {
      await medusaAdminService.deleteProduct(dynamicStoreHandle, productId);
      showToast('Product deleted successfully', 'success');
      
      // Remove the deleted product from the local state
      setProducts(prev => prev.filter(product => product.id !== productId));
      setSelectedProducts(prev => prev.filter(id => id !== productId));
      setTotalCount(prev => prev - 1);
      
      // Close the modal
      setDeleteConfirmModal({ isOpen: false, productId: '', productName: '' });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to delete product';
      showToast(errorMessage, 'error');
    } finally {
      setDeletingProductId(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmModal({ isOpen: false, productId: '', productName: '' });
  };

  const handleImportComplete = () => {
    // Refresh the products list after successful import
    fetchProducts();
  };

  const formatPrice = (price: number | string) => {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    if (isNaN(numPrice)) return 'N/A';
    
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(numPrice);
  };
  
  return (
    <Box sx={{ p: 3, maxWidth: '100%' }}>
      {/* Page Header */}
      <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Products
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your product inventory
          </Typography>
        </Box>
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<ImportIcon />}
            onClick={() => setIsImportModalOpen(true)}
          >
            Import
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigateToAddProduct(dynamicStoreHandle)}
          >
            Add Product
          </Button>
        </Stack>
      </Stack>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            alignItems={{ xs: 'stretch', sm: 'center' }}
            justifyContent="space-between"
          >
            <Stack direction="row" spacing={2} alignItems="center" sx={{ flex: 1 }}>
              <TextField
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{ minWidth: 300 }}
              />
              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  label="Status"
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="draft">Draft</MenuItem>
                  <MenuItem value="published">Published</MenuItem>
                </Select>
              </FormControl>
            </Stack>
            <Typography variant="body2" color="text.secondary">
              {totalCount > 0 ? `${totalCount} total products` : `${filteredProducts.length} products`}
            </Typography>
          </Stack>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedProducts.length > 0 && (
        <Alert
          severity="info"
          sx={{ mb: 3 }}
          action={
            <Stack direction="row" spacing={1}>
              <Button size="small" variant="outlined">
                Bulk Edit
              </Button>
              <Button size="small" variant="outlined">
                Archive
              </Button>
              <Button size="small" variant="contained" color="error">
                Delete
              </Button>
            </Stack>
          }
        >
          {selectedProducts.length} product(s) selected
        </Alert>
      )}

      {/* Error Display */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={() => window.location.reload()}>
              Retry
            </Button>
          }
        >
          <strong>Error loading products:</strong> {error}
        </Alert>
      )}

      {/* Products Table */}
      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : filteredProducts.length === 0 ? (
        /* No Products Message */
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <Box
              sx={{
                width: 64,
                height: 64,
                borderRadius: '50%',
                bgcolor: 'action.hover',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mx: 'auto',
                mb: 2,
              }}
            >
              <ProductIcon sx={{ fontSize: 32, color: 'text.secondary' }} />
            </Box>
            <Typography variant="h6" gutterBottom>
              {searchTerm || statusFilter !== 'all' ? 'No products found' : 'No products available'}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your search criteria or filters to find products.'
                : 'Get started by adding your first product to the store.'
              }
            </Typography>
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center">
              {(searchTerm || statusFilter !== 'all') && (
                <Button
                  variant="outlined"
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('all');
                  }}
                >
                  Clear Filters
                </Button>
              )}
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => navigateToAddProduct(dynamicStoreHandle)}
              >
                Add Your First Product
              </Button>
            </Stack>
          </CardContent>
        </Card>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Product</TableCell>
                <TableCell>Handle</TableCell>
                <TableCell>Price</TableCell>
                <TableCell>Stock</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Category</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredProducts.map((product) => {
                const availableVariant = product.variants.find(v =>
                  v.metadata?.product_quantity > 0
                );
                const productId = product.id;
                const productName = product.title || '-';
                const productHandle = product.handle || '-';
                const productPrice = availableVariant?.metadata?.original_price || product.variants?.[0]?.metadata?.original_price || 0;
                const productDiscountedPrice = availableVariant?.metadata?.sale_price || product.variants?.[0]?.metadata?.sale_price || 0;
                const productStock = availableVariant?.metadata?.product_quantity || 0;
                const productStatus = product.status || 'draft';
                const productCategory = product.categories?.[0]?.name || '-';
                const productImage = product.thumbnail || product.images?.[0]?.url;
                
                const getStatusColor = (status: string) => {
                  switch (status) {
                    case 'published':
                    case 'active':
                      return 'success';
                    case 'draft':
                      return 'warning';
                    case 'archived':
                      return 'default';
                    default:
                      return 'default';
                  }
                };
                
                return (
                  <TableRow key={productId} hover>
                    <TableCell>
                      <Stack direction="row" spacing={2} alignItems="center">
                        <Box
                          sx={{
                            width: 48,
                            height: 48,
                            borderRadius: 1,
                            bgcolor: 'action.hover',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            overflow: 'hidden',
                          }}
                        >
                          {productImage ? (
                            <Image
                              src={productImage}
                              alt={productName}
                              width={48}
                              height={48}
                              style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                            />
                          ) : (
                            <ProductIcon sx={{ color: 'text.secondary' }} />
                          )}
                        </Box>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {productName}
                          </Typography>
                        </Box>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{productHandle}</Typography>
                    </TableCell>
                    <TableCell>
                      <Stack>
                        <Typography
                          variant="body2"
                          sx={{ textDecoration: 'line-through', color: 'text.secondary' }}
                        >
                          {formatPrice(productPrice)}
                        </Typography>
                        <Typography variant="body2" color="error.main" fontWeight="medium">
                          {formatPrice(productDiscountedPrice)}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Typography
                        variant="body2"
                        color={Number(productStock) <= 10 ? 'error.main' : 'text.primary'}
                        fontWeight={Number(productStock) <= 10 ? 'medium' : 'normal'}
                      >
                        {productStock}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={String(productStatus).charAt(0).toUpperCase() + String(productStatus).slice(1)}
                        color={getStatusColor(productStatus) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{productCategory}</Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Stack direction="row" spacing={1} justifyContent="flex-end">
                        <IconButton
                          size="small"
                          onClick={() => navigateToEditProduct(dynamicStoreHandle, productId)}
                          color="primary"
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteClick(productId, productName)}
                          disabled={deletingProductId === productId}
                          color="error"
                        >
                          {deletingProductId === productId ? (
                            <CircularProgress size={16} />
                          ) : (
                            <DeleteIcon fontSize="small" />
                          )}
                        </IconButton>
                      </Stack>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
          
          {/* Pagination */}
          <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="body2" color="text.secondary">
                Showing 1 to {filteredProducts.length} of {filteredProducts.length} results
              </Typography>
              <Stack direction="row" spacing={1}>
                <Button variant="outlined" size="small" disabled>
                  Previous
                </Button>
                <Button variant="contained" size="small">
                  1
                </Button>
                <Button variant="outlined" size="small" disabled>
                  Next
                </Button>
              </Stack>
            </Stack>
          </Box>
        </TableContainer>
      )}

      {/* Bulk Import Modal */}
      <BulkImportModal
        isOpen={isImportModalOpen}
        onClose={() => setIsImportModalOpen(false)}
        onImportComplete={handleImportComplete}
        storeHandle={dynamicStoreHandle}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteConfirmModal.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Product"
        message={`Are you sure you want to delete the product "${deleteConfirmModal.productName}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonColor="red"
        icon="danger"
        isLoading={deletingProductId === deleteConfirmModal.productId}
      />
    </Box>
  );
};