'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Stack,
  Alert,
  LinearProgress,
  Chip,
  Grid,
  Card,
  CardContent,
  IconButton,
  CircularProgress,
} from '@mui/material';
import {
  Close,
  Download,
  Upload,
  CheckCircle,
  Error,
  Info,
} from '@mui/icons-material';
import { useToast } from '@/app/providers/toast-provider';
import { bulkImportApi } from '@/lib/api/bulk-import';
import { categoriesApi } from '@/lib/api/categories';

interface BulkImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImportComplete: () => void;
  storeHandle: string;
}

interface ApiCategory {
  id: string;
  name: string;
  handle: string;
  description: string;
  parent_category_id?: string;
  created_at?: string;
  updated_at?: string;
}

export const BulkImportModal: React.FC<BulkImportModalProps> = ({
  isOpen,
  onClose,
  onImportComplete,
  storeHandle
}) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadResults, setUploadResults] = useState<{
    success: boolean;
    message?: string;
    error?: string;
  } | null>(null);
  const [categories, setCategories] = useState<ApiCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);

  const { showToast } = useToast();

  // Fetch categories when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchCategories();
    }
  }, [isOpen]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setUploadedFile(null);
      setUploadResults(null);
      setUploadProgress(0);
      setIsProcessing(false);
    }
  }, [isOpen]);

  const fetchCategories = async () => {
    setIsLoadingCategories(true);
    try {
      const result = await categoriesApi.getCategories();
      if (result.success && result.data) {
        setCategories(result.data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setIsLoadingCategories(false);
    }
  };

  // Download XLSX template from API
  const downloadTemplate = async () => {
    setIsDownloading(true);
    try {
      console.log('Downloading template...');
      const blob = await bulkImportApi.downloadTemplate();
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${storeHandle}-product-import-template.xlsx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      showToast('Template downloaded successfully!', 'success');
    } catch (error) {
      console.error('Error downloading template:', error);
      showToast('Failed to download template. Please try again.', 'error');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Check if file is Excel format
      const isExcelFile = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                          file.type === 'application/vnd.ms-excel' ||
                          file.name.endsWith('.xlsx') ||
                          file.name.endsWith('.xls');
      
      if (!isExcelFile) {
        showToast('Please upload an Excel file (.xlsx or .xls)', 'error');
        return;
      }
      
      setUploadedFile(file);
      setUploadResults(null);
    }
  };

  const processUpload = async () => {
    if (!uploadedFile) return;

    setIsProcessing(true);
    setUploadProgress(0);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      // Upload file to API
      const result = await bulkImportApi.uploadProductImport(uploadedFile);
      
      // Clear progress interval
      clearInterval(progressInterval);
      setUploadProgress(100);

      if (result.success) {
        setUploadResults({
          success: true,
          message: result.message || 'Products imported successfully!',
        });
        showToast(result.message || 'Products imported successfully!', 'success');
        
        // Call the callback to refresh the products list
        onImportComplete();
      } else {
        setUploadResults({
          success: false,
          error: result.error || 'Failed to import products',
        });
        showToast(result.error || 'Failed to import products', 'error');
      }
    } catch (error) {
      console.error('Error processing upload:', error);
      setUploadResults({
        success: false,
        error: 'An error occurred while processing the file',
      });
      showToast('An error occurred while processing the file', 'error');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClose = () => {
    if (!isProcessing) {
      onClose();
    }
  };

  const mainCategories = categories.filter(cat => !cat.parent_category_id);
  const subcategories = categories.filter(cat => cat.parent_category_id);

  return (
    <Dialog
      open={isOpen}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      disableEscapeKeyDown={isProcessing}
    >
      <DialogTitle>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h6" component="h2">
              Bulk Import Products
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              Import multiple products at once using an Excel file.
            </Typography>
          </Box>
          <IconButton
            onClick={handleClose}
            disabled={isProcessing}
            size="small"
            aria-label="Close dialog"
          >
            <Close />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent sx={{ maxHeight: '70vh', overflow: 'auto' }}>
        <Stack spacing={3} sx={{ mt: 1 }}>
          {/* Template Download Section */}
          <Alert severity="info" >
            <Stack direction="row" spacing={2} alignItems="flex-start">
              <Download color="info" />
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  Download Template
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Start by downloading our Excel template with sample data and instructions.
                </Typography>
                <Button
                  onClick={downloadTemplate}
                  disabled={isDownloading}
                  variant="contained"
                  size="small"
                  startIcon={isDownloading ? <CircularProgress size={16} /> : <Download />}
                >
                  {isDownloading ? 'Downloading...' : 'Download Excel Template'}
                </Button>
              </Box>
            </Stack>
          </Alert>

          {/* Instructions */}
          <Card variant="outlined">
            <CardContent>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>
                Instructions
              </Typography>
              <Stack spacing={1.5}>
                <Stack direction="row" spacing={2} alignItems="flex-start">
                  <Chip label="1" size="small" color="primary" />
                  <Typography variant="body2">
                    Download the Excel template above
                  </Typography>
                </Stack>
                <Stack direction="row" spacing={2} alignItems="flex-start">
                  <Chip label="2" size="small" color="primary" />
                  <Typography variant="body2">
                    Fill in your product data following the sample format
                  </Typography>
                </Stack>
                <Stack direction="row" spacing={2} alignItems="flex-start">
                  <Chip label="3" size="small" color="primary" />
                  <Typography variant="body2">
                    Make sure category names match exactly with your existing categories
                  </Typography>
                </Stack>
                <Stack direction="row" spacing={2} alignItems="flex-start">
                  <Chip label="4" size="small" color="primary" />
                  <Typography variant="body2">
                    Save the file in Excel format (.xlsx) and upload it below
                  </Typography>
                </Stack>
              </Stack>
            </CardContent>
          </Card>

          {/* Available Categories */}
          {/* {categories.length > 0 && (
            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  Available Categories
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
                  Use these exact category names in your Excel file:
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="caption" sx={{ fontWeight: 500, mb: 1, display: 'block' }}>
                      Categories:
                    </Typography>
                    <Box sx={{ maxHeight: 80, overflow: 'auto' }}>
                      <Stack spacing={0.5}>
                        {mainCategories.length > 0 ? (
                          mainCategories.map((category) => (
                            <Chip
                              key={category.id}
                              label={category.name}
                              size="small"
                              variant="outlined"
                            />
                          ))
                        ) : (
                          <Typography variant="caption" color="text.disabled" sx={{ fontStyle: 'italic' }}>
                            No categories available
                          </Typography>
                        )}
                      </Stack>
                    </Box>
                  </Grid>

                  {subcategories.length > 0 && (
                    <Grid item xs={12} md={6}>
                      <Typography variant="caption" sx={{ fontWeight: 500, mb: 1, display: 'block' }}>
                        Subcategories:
                      </Typography>
                      <Box sx={{ maxHeight: 80, overflow: 'auto' }}>
                        <Stack spacing={0.5}>
                          {subcategories.map((subcategory) => {
                            const parentCategory = mainCategories.find(cat => cat.id === subcategory.parent_category_id);
                            return (
                              <Chip
                                key={subcategory.id}
                                label={`${subcategory.name} (${parentCategory?.name})`}
                                size="small"
                                variant="outlined"
                                color="secondary"
                              />
                            );
                          })}
                        </Stack>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </CardContent>
            </Card>
          )} */}

          {/* File Upload Section */}
          <Card
            variant="outlined"
            sx={{
              border: '2px dashed',
              borderColor: 'divider',
              bgcolor: 'background.paper',
              textAlign: 'center',
              p: 3
            }}
          >
            <Stack alignItems="center" spacing={2}>
              <Upload sx={{ fontSize: 32, color: 'text.disabled' }} />
              <Box>
                <Button
                  component="label"
                  variant="text"
                  sx={{ textTransform: 'none' }}
                >
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    Upload your Excel file
                  </Typography>
                  <input
                    type="file"
                    accept=".xlsx,.xls,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                    onChange={handleFileUpload}
                    hidden
                  />
                </Button>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                  or drag and drop
                </Typography>
              </Box>
              <Typography variant="caption" color="text.secondary">
                Excel files only (.xlsx, .xls), up to 10MB
              </Typography>
            </Stack>

            {uploadedFile && (
              <Alert severity="success" sx={{ mt: 2, textAlign: 'left' }} >
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {uploadedFile.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      ({(uploadedFile.size / 1024).toFixed(1)} KB)
                    </Typography>
                  </Stack>
                  <IconButton
                    onClick={() => {
                      setUploadedFile(null);
                      setUploadResults(null);
                    }}
                    size="small"
                    color="success"
                  >
                    <Close />
                  </IconButton>
                </Stack>
              </Alert>
            )}
          </Card>

          {/* Progress Bar */}
          {isProcessing && (
            <Card variant="outlined">
              <CardContent>
                <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    Importing products...
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {Math.round(uploadProgress)}%
                  </Typography>
                </Stack>
                <LinearProgress
                  variant="determinate"
                  value={uploadProgress}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </CardContent>
            </Card>
          )}

          {/* Upload Results */}
          {uploadResults && (
            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>
                  Import Results
                </Typography>

                {uploadResults.success ? (
                  <Alert severity="success" icon={<CheckCircle />}>
                    <Typography variant="body2">
                      {uploadResults.message}
                    </Typography>
                  </Alert>
                ) : (
                  <Alert severity="error" icon={<Error />}>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      Import failed:
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 0.5 }}>
                      {uploadResults.error}
                    </Typography>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}
        </Stack>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button
          onClick={handleClose}
          disabled={isProcessing}
          variant="outlined"
        >
          {uploadResults?.success ? 'Close' : 'Cancel'}
        </Button>

        {uploadedFile && (
          <Button
            onClick={processUpload}
            disabled={isProcessing}
            variant="contained"
            color="success"
            startIcon={isProcessing ? <CircularProgress size={16} /> : <Upload />}
          >
            {isProcessing ? 'Importing...' : 'Import Products'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};