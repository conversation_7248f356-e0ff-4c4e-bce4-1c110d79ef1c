'use client';

import React, { useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useNavigationWithLoading } from '@/hooks/useNavigationWithLoading';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Card,
  CardContent,
  Stack,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Chip,
  Stepper,
  Step,
  StepLabel,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@mui/material';
import {
  ArrowBack,
  Save,
  NavigateBefore,
  NavigateNext,
  Publish as PublishIcon,
} from '@mui/icons-material';
import { useProductStore } from '@/store/productStore';
import { ImageUpload } from '@/components/ui/ImageUploadBasic';
import { LexicalContentEditor } from '@/components/ui/LexicalContentEditor';
import { ProductOptions } from './ProductOptionsBasic';
import { ProductVariants } from './ProductVariantsBasic';
import { medusaAdminService } from '@/lib/api/medusa-admin';
import { useToast } from '@/app/providers/toast-provider';

const steps = [
  'Basic Information',
  'Images & Media',
  'Options & Variants',
  'Content & SEO',
  'Review & Update',
];



export const EditProductForm: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const productId = params.productId as string;
  const { showToast } = useToast();
  const { navigateToProducts } = useNavigationWithLoading();
  const [categories, setCategories] = React.useState<any[]>([]);
  const [collections, setCollections] = React.useState<any[]>([]);
  const [tags, setTags] = React.useState<any[]>([]);
  const [isLoadingData, setIsLoadingData] = React.useState(false);
  const [maxHeight, setMaxHeight] = React.useState(100);
  const [showPublishDialog, setShowPublishDialog] = React.useState(false);
  const {
    formData,
    currentStep,
    isLoading,
    errors,
    setCurrentStep,
    updateField,
    setThumbnail,
    generateVariantsFromOptions,
    updateProduct,
    publishProduct,
    resetForm,
    setEditMode,
    loadProductForEdit,
  } = useProductStore();

  // Load categories, collections, and tags
  useEffect(() => {
    const loadData = async () => {
      setIsLoadingData(true);
      try {
        const [categoriesData, collectionsData, tagsData] = await Promise.all([
          medusaAdminService.getCategories(storeHandle),
          medusaAdminService.getCollections(storeHandle),
          medusaAdminService.getTags(storeHandle)
        ]);
        
        setCategories(categoriesData || []);
        setCollections(collectionsData || []);
        setTags(tagsData || []);
      } catch (error) {
        console.error('Error loading form data:', error);
        showToast('Failed to load form data', 'error');
      } finally {
        setIsLoadingData(false);
      }
    };
    
    if (storeHandle) {
      loadData();
    }
  }, [storeHandle, showToast]);

  // Load product data when component mounts
  useEffect(() => {
    const loadProduct = async () => {
      setEditMode(true, productId);
      const success = await loadProductForEdit(productId);
      if (!success) {
        showToast('Failed to load product data', 'error');
        router.push(`/${storeHandle}/admin/products`);
      }
    };

    loadProduct();

    // Cleanup on unmount
    return () => {
      resetForm();
    };
  }, [productId, setEditMode, loadProductForEdit, resetForm, router, storeHandle, showToast]);

  // Validation functions for each step
  const validateCurrentStep = () => {
    const errors: Record<string, string> = {};
    
    switch (currentStep) {
      case 0: // Basic Information
        if (!formData.productName?.trim()) {
          errors.productName = 'Product name is required';
        }
        if (!formData.productHandle?.trim()) {
          errors.productHandle = 'Product handle is required';
        }
        if (!formData.productDescription?.trim()) {
          errors.productDescription = 'Product description is required';
        }
        if (!formData.productCategory?.trim()) {
          errors.productCategory = 'Product category is required';
        }
        break;
        
      case 1: // Images & Media
        if (!formData.productImages || formData.productImages.length === 0) {
          errors.productImages = 'At least one product image is required';
        }
        break;
        
      case 2: // Options & Variants
        if (!formData.productVariants || formData.productVariants.length === 0) {
          errors.productVariants = 'At least one product variant is required';
        }
        break;
        
      case 3: // Content & SEO
        if (!formData.productOverview?.trim()) {
          errors.productOverview = 'Product overview is required';
        }
        if (!formData.productFeatures?.trim()) {
          errors.productFeatures = 'Product features are required';
        }
        if (!formData.productSpecifications?.trim()) {
          errors.productSpecifications = 'Product specifications are required';
        }
        break;
    }
    
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (validateCurrentStep() && currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const validateAllRequiredFields = () => {
    const errors: Record<string, string> = {};
    
    // Basic Information
    if (!formData.productName?.trim()) {
      errors.productName = 'Product name is required';
    }
    if (!formData.productHandle?.trim()) {
      errors.productHandle = 'Product handle is required';
    }
    if (!formData.productDescription?.trim()) {
      errors.productDescription = 'Product description is required';
    }
    if (!formData.productCategory?.trim()) {
      errors.productCategory = 'Product category is required';
    }
    
    // Images
    if (!formData.productImages || formData.productImages.length === 0) {
      errors.productImages = 'At least one product image is required';
    }
    
    // Variants
    if (!formData.productVariants || formData.productVariants.length === 0) {
      errors.productVariants = 'At least one product variant is required';
    }
    
    // Content
    if (!formData.productOverview?.trim()) {
      errors.productOverview = 'Product overview is required';
    }
    if (!formData.productFeatures?.trim()) {
      errors.productFeatures = 'Product features are required';
    }
    if (!formData.productSpecifications?.trim()) {
      errors.productSpecifications = 'Product specifications are required';
    }
    
    return Object.keys(errors).length === 0;
  };

  const handleUpdate = async () => {
    // Validate all required fields before updating
    if (!validateAllRequiredFields()) {
      showToast('Please fill in all required fields before updating', 'error');
      // Find the first step with errors and navigate to it
      const errorFields = Object.keys(errors);
      if (errorFields.some(field => ['productName', 'productHandle', 'productDescription', 'productCategory'].includes(field))) {
        setCurrentStep(0);
      } else if (errorFields.includes('productImages')) {
        setCurrentStep(1);
      } else if (errorFields.includes('productVariants')) {
        setCurrentStep(2);
      } else if (errorFields.some(field => ['productOverview', 'productFeatures', 'productSpecifications'].includes(field))) {
        setCurrentStep(3);
      }
      return;
    }

    try {
      const success = await updateProduct();
      if (success) {
        showToast('Product updated successfully', 'success');
        // router.push(`/${storeHandle}/admin/products`);
      } else {
        showToast('Failed to update product', 'error');
      }
    } catch (error) {
      console.error('Error updating product:', error);
      showToast('Failed to update product', 'error');
    }
  };

  const handlePublishClick = () => {
    // Validate all required fields before showing publish dialog
    if (!validateAllRequiredFields()) {
      showToast('Please fill in all required fields before publishing', 'error');
      // Find the first step with errors and navigate to it
      const errorFields = Object.keys(errors);
      if (errorFields.some(field => ['productName', 'productHandle', 'productDescription', 'productCategory'].includes(field))) {
        setCurrentStep(0);
      } else if (errorFields.includes('productImages')) {
        setCurrentStep(1);
      } else if (errorFields.includes('productVariants')) {
        setCurrentStep(2);
      } else if (errorFields.some(field => ['productOverview', 'productFeatures', 'productSpecifications'].includes(field))) {
        setCurrentStep(3);
      }
      return;
    }
    
    setShowPublishDialog(true);
  };

  const handlePublishConfirm = async () => {
    setShowPublishDialog(false);
    
    try {
      const success = await publishProduct();
      if (success) {
        showToast('Product published successfully', 'success');
        // router.push(`/${storeHandle}/admin/products`);
      } else {
        showToast('Failed to publish product', 'error');
      }
    } catch (error) {
      console.error('Error publishing product:', error);
      showToast('Failed to publish product', 'error');
    }
  };

  const handlePublishCancel = () => {
    setShowPublishDialog(false);
  };

  const handleBackToProducts = () => {
    resetForm();
    navigateToProducts(storeHandle);
  };

  const handleOpen = (event) => {
    // Calculate the space below the anchor element (the select input)
    const anchorRect = event.currentTarget.getBoundingClientRect();
    const availableHeight = window.innerHeight - anchorRect.bottom - 16; // 16px for padding
    setMaxHeight(Math.max(availableHeight, 115)); // 115 as minimum
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Stack spacing={3}>
            <Stack direction={{ xs: 'column', md: 'row' }} spacing={3}>
              <TextField
                fullWidth
                label="Product Name"
                value={formData.productName || ''}
                onChange={(e) => updateField('productName', e.target.value)}
                error={!!errors.productName}
                helperText={errors.productName}
                placeholder="Enter product name"
                required
              />

              <TextField
                fullWidth
                label="Product Handle"
                value={formData.productHandle || ''}
                onChange={(e) => updateField('productHandle', e.target.value)}
                error={!!errors.productHandle}
                helperText={errors.productHandle || 'URL-friendly version of product name'}
                placeholder="product-handle"
                required
              />
            </Stack>

            <TextField
              fullWidth
              label="Product Subtitle"
              value={formData.productSubtitle || ''}
              onChange={(e) => updateField('productSubtitle', e.target.value)}
              placeholder="Short description that appears below the product name"
            />

            <TextField
              fullWidth
              label="Product Description"
              value={formData.productDescription || ''}
              onChange={(e) => updateField('productDescription', e.target.value)}
              multiline
              rows={4}
              error={!!errors.productDescription}
              helperText={errors.productDescription}
              placeholder="Describe your product..."
              required
            />

            <Stack direction={{ xs: 'column', md: 'row' }} spacing={3}>
              <FormControl fullWidth error={!!errors.productCategory} required>
                <InputLabel>Product Category</InputLabel>
                <Select
                  value={formData.productCategory || ''}
                  label="Product Category"
                  onChange={(e) => updateField('productCategory', e.target.value)}
                  disabled={isLoadingData}
                  onOpen={handleOpen}
                  MenuProps={{
                    anchorOrigin: { vertical: 'bottom', horizontal: 'left' },
                    transformOrigin: { vertical: 'top', horizontal: 'left' },
                    PaperProps: { style: { maxHeight: maxHeight } },
                  }}

                >
                  {/* <MenuItem value="">Select a category</MenuItem> */}
                  {categories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
                {errors.productCategory && (
                  <FormHelperText>{errors.productCategory}</FormHelperText>
                )}
              </FormControl>

              <FormControl fullWidth>
                <InputLabel>Product Collection</InputLabel>
                <Select
                  value={formData.productCollection || ''}
                  label="Product Collection"
                  onChange={(e) => updateField('productCollection', e.target.value)}
                  disabled={isLoadingData}
                  MenuProps={{
                    anchorOrigin: { vertical: 'bottom', horizontal: 'left' },
                    transformOrigin: { vertical: 'top', horizontal: 'left' },
                    PaperProps: { style: { maxHeight: maxHeight } },
                  }}
                >
                  <MenuItem value="">None</MenuItem>
                  {collections.map((collection) => (
                    <MenuItem key={collection.id} value={collection.id}>
                      {collection.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Stack>

            <Box>
              <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 500 }}>
                Product Tags
              </Typography>
              <Stack spacing={2}>
                <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1 }}>
                  {(formData.productTags || []).map((tag, index) => (
                    <Chip
                      key={index}
                      label={tag}
                      onDelete={() => {
                        const newTags = [...(formData.productTags || [])];
                        newTags.splice(index, 1);
                        updateField('productTags', newTags);
                      }}
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Stack>
                <FormControl fullWidth>
                  <InputLabel>Add Tags</InputLabel>
                  <Select
                    value=""
                    label="Add Tags"
                    onChange={(e) => {
                      if (e.target.value && !(formData.productTags || []).includes(e.target.value)) {
                        updateField('productTags', [...(formData.productTags || []), e.target.value]);
                      }
                    }}
                    disabled={isLoadingData}
                    MenuProps={{
                      anchorOrigin: { vertical: 'bottom', horizontal: 'left' },
                      transformOrigin: { vertical: 'top', horizontal: 'left' },
                      PaperProps: { style: { maxHeight: maxHeight } },
                    }}
                  >
                    <MenuItem value="">{isLoadingData ? 'Loading tags...' : 'Select tags'}</MenuItem>
                    {tags.map((tag) => (
                      <MenuItem key={tag.id} value={tag.value}>
                        {tag.value}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Stack>
            </Box>
          </Stack>
        );

      case 1:
        return (
          <div>
            <ImageUpload
              images={formData.productImages || []}
              onImagesChange={(images) => updateField('productImages', images)}
              thumbnailId={formData.productThumbnail?.id}
              onThumbnailChange={(imageId) => {
                const image = formData.productImages?.find(img => img.id === imageId);
                if (image) {
                  setThumbnail(image);
                }
              }}
              label="Product Images *"
              error={errors.productImages}
              helperText="Upload high-quality images of your product. The first image will be used as the thumbnail."
            />
          </div>
        );

      case 2:
        return (
          <div className="space-y-8">
            <ProductOptions
              options={formData.productOptions || []}
              onChange={(options) => updateField('productOptions', options)}
              onGenerateVariants={generateVariantsFromOptions}
              error={errors.productOptions}
            />
            <div className="border-t border-gray-200 pt-8">
              <ProductVariants
                variants={formData.productVariants || []}
                onChange={(variants) => updateField('productVariants', variants)}
                error={errors.productVariants}
              />
            </div>
          </div>
        );

      case 3:
        return (
          <Stack spacing={4}>
            <LexicalContentEditor
              label="Product Overview"
              value={formData.productOverview || ''}
              onChange={(value) => updateField('productOverview', value)}
              placeholder="Provide a detailed overview of your product. Describe what makes it unique, its main purpose, and why customers should choose it..."
              height={250}
              error={errors.productOverview}
              helperText="Write a compelling overview that highlights your product's value proposition"
              required
            />
            <LexicalContentEditor
              label="Product Features"
              value={formData.productFeatures || ''}
              onChange={(value) => updateField('productFeatures', value)}
              placeholder="List the key features and benefits of your product. Use bullet points or numbered lists for better readability..."
              height={250}
              error={errors.productFeatures}
              helperText="Highlight the most important features that set your product apart"
              required
            />
            <LexicalContentEditor
              label="Product Specifications"
              value={formData.productSpecifications || ''}
              onChange={(value) => updateField('productSpecifications', value)}
              placeholder="Include technical specifications, dimensions, materials, compatibility, warranty information, etc..."
              height={250}
              error={errors.productSpecifications}
              helperText="Provide detailed technical information that customers need to make informed decisions"
              required
            />
          </Stack>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-blue-800">Review all product changes before updating. Your changes will be saved to the existing product.</span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">Basic Information</h3>
                <div className="space-y-2 text-sm">
                  <p><strong>Name:</strong> {formData.productName}</p>
                  <p><strong>Handle:</strong> {formData.productHandle}</p>
                  <p><strong>Category:</strong> {categories.find(c => c.id === formData.productCategory)?.name || 'None'}</p>
                  <p><strong>Collection:</strong> {collections.find(c => c.id === formData.productCollection)?.title || 'None'}</p>
                  <p><strong>Status:</strong> <span className="capitalize">{formData.status}</span></p>
                </div>
              </div>
              
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">Pricing & Inventory</h3>
                <div className="space-y-2 text-sm">
                  <p><strong>Variants:</strong> {formData.productVariants?.length || 0}</p>
                  <p><strong>Starting Price:</strong> ₹{formData.productSalePrice || 0}</p>
                  <p><strong>Total Stock:</strong> {formData.productStock || 0}</p>
                </div>
              </div>
              
              <div className="md:col-span-2 bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">Images</h3>
                <div className="space-y-2 text-sm">
                  <p><strong>Product Images:</strong> {formData.productImages?.length || 0} uploaded</p>
                  <p><strong>Thumbnail:</strong> {formData.productThumbnail ? 'Set' : 'Not set'}</p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Show loading state while loading product data
  if (isLoading && !formData.productName) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <svg className="animate-spin w-8 h-8 text-blue-600 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p className="text-gray-600">Loading product data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Stack spacing={3} sx={{ mb: 4 }}>
        <Button
          startIcon={<ArrowBack />}
          onClick={handleBackToProducts}
          sx={{ alignSelf: 'flex-start' }}
        >
          Back to Products
        </Button>
        <Box>
          <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 1 }}>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
              Edit Product
            </Typography>
            <Chip
              label={formData.status === 'draft' ? 'Draft' : 'Published'}
              color={formData.status === 'draft' ? 'warning' : 'success'}
              variant="filled"
              size="small"
              sx={{
                fontWeight: 600,
                textTransform: 'capitalize',
              }}
            />
          </Stack>
          <Typography variant="body1" color="text.secondary">
            Update your product information, images, and variants.
            {formData.status === 'draft' && ' Complete all required fields to publish your product.'}
          </Typography>
        </Box>
      </Stack>

      {/* Stepper */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Stepper activeStep={currentStep} alternativeLabel>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </CardContent>
      </Card>

      {/* Form Content */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h5" component="h2" sx={{ mb: 3, fontWeight: 600 }}>
            {steps[currentStep]}
          </Typography>
          
          {/* Validation Errors Alert */}
          {Object.keys(errors).length > 0 && (
            <Alert severity="error" sx={{ mb: 3 }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                Please fix the following errors:
              </Typography>
              <ul style={{ margin: 0, paddingLeft: '20px' }}>
                {Object.entries(errors).map(([field, message]) => (
                  <li key={field}>
                    <Typography variant="body2">{message}</Typography>
                  </li>
                ))}
              </ul>
            </Alert>
          )}
          
          {renderStepContent(currentStep)}
        </CardContent>
      </Card>

      {/* Navigation */}
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Button
          startIcon={<NavigateBefore />}
          onClick={handleBack}
          disabled={currentStep === 0}
          variant="outlined"
        >
          Back
        </Button>

        <Stack direction="row" spacing={2}>
          {currentStep === steps.length - 1 ? (
            <>
              <Button
                startIcon={isLoading ? <CircularProgress size={20} /> : <Save />}
                onClick={handleUpdate}
                disabled={isLoading}
                variant="contained"
              >
                Update Product
              </Button>
              {/* Show Publish button only if product status is draft */}
              {formData.status === 'draft' && (
                <Button
                  startIcon={isLoading ? <CircularProgress size={20} /> : <PublishIcon />}
                  onClick={handlePublishClick}
                  disabled={isLoading}
                  variant="contained"
                  color="success"
                  sx={{
                    bgcolor: 'success.main',
                    '&:hover': {
                      bgcolor: 'success.dark',
                    },
                  }}
                >
                  Publish Product
                </Button>
              )}
            </>
          ) : (
            <Button
              endIcon={<NavigateNext />}
              onClick={handleNext}
              variant="contained"
              disabled={!validateCurrentStep()}
            >
              Next
            </Button>
          )}
        </Stack>
      </Stack>

      {/* Publish Confirmation Dialog */}
      <Dialog
        open={showPublishDialog}
        onClose={handlePublishCancel}
        aria-labelledby="publish-dialog-title"
        aria-describedby="publish-dialog-description"
      >
        <DialogTitle id="publish-dialog-title">
          Publish Product?
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="publish-dialog-description">
            Are you sure you want to publish this product? Once published, the product will be visible to customers and available for purchase.
            <br /><br />
            <strong>Product:</strong> {formData.productName}
            <br />
            <strong>Status will change from:</strong> Draft → Published
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handlePublishCancel} color="inherit">
            Cancel
          </Button>
          <Button 
            onClick={handlePublishConfirm} 
            variant="contained" 
            color="success"
            startIcon={<PublishIcon />}
            disabled={isLoading}
          >
            {isLoading ? 'Publishing...' : 'Publish Product'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};