'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useNavigationWithLoading } from '@/hooks/useNavigationWithLoading';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  IconButton,
  Stack,
  CircularProgress,
  InputAdornment,
  Pagination,
} from '@mui/material';
import {
  Add,
  Search,
  Edit,
  Delete,
  Collections,
} from '@mui/icons-material';
import { AdminTableLoading } from '../AdminLoading';
import { useAuthStore } from '@/stores/authStore';
import { medusaAdminService, type MedusaCollection } from '@/lib/api/medusa-admin';
import { useToast } from '@/app/providers/toast-provider';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';

// Removed mock data - now using API

export const AdminCollectionsList: React.FC = () => {
  const params = useParams();
  const storeHandle = params.storeHandle as string;
  const { getStoreHandle } = useAuthStore();
  const { showToast } = useToast();
  const { navigateWithLoading } = useNavigationWithLoading();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCollections, setSelectedCollections] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [collections, setCollections] = useState<MedusaCollection[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [deletingCollectionId, setDeletingCollectionId] = useState<string | null>(null);
  const [deleteConfirmModal, setDeleteConfirmModal] = useState<{
    isOpen: boolean;
    collectionId: string;
    collectionName: string;
  }>({ isOpen: false, collectionId: '', collectionName: '' });
  
  // Get dynamic store handle from auth store
  const dynamicStoreHandle = getStoreHandle() || storeHandle;

  // Fetch collections from API
  useEffect(() => {
    const fetchCollections = async () => {
      if (!dynamicStoreHandle) {
        console.log('No store handle available for collections API call');
        setIsLoading(false);
        return;
      }
      
      setIsLoading(true);
      setError(null);
      
      try {
        console.log('=== FETCHING COLLECTIONS ===');
        console.log('Using store handle as x-tenant-id:', dynamicStoreHandle);
        
        const collectionsArray = await medusaAdminService.getCollections(dynamicStoreHandle);
        
        console.log('Collections fetched successfully:', collectionsArray);
        
        // Filter collections by search term if provided
        let filteredCollections = collectionsArray;
        if (searchTerm) {
          filteredCollections = collectionsArray.filter(collection => 
            collection.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (collection.handle && collection.handle.toLowerCase().includes(searchTerm.toLowerCase()))
          );
        }
        
        setCollections(filteredCollections);
        setTotalCount(collectionsArray.length);
      } catch (error: any) {
        console.error('Failed to fetch collections:', error);
        setError(error.message || 'Failed to fetch collections');
        setCollections([]);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCollections();
  }, [dynamicStoreHandle, searchTerm]);

  // Collections are already filtered by the API based on search
  const filteredCollections = collections;

  const handleSelectAll = () => {
    if (selectedCollections.length === filteredCollections.length) {
      setSelectedCollections([]);
    } else {
      setSelectedCollections(filteredCollections.map(c => c.id));
    }
  };

  const handleSelectCollection = (collectionId: string) => {
    setSelectedCollections(prev =>
      prev.includes(collectionId)
        ? prev.filter(id => id !== collectionId)
        : [...prev, collectionId]
    );
  };

  const handleDeleteClick = (collectionId: string, collectionName: string) => {
    setDeleteConfirmModal({
      isOpen: true,
      collectionId,
      collectionName
    });
  };

  const handleDeleteConfirm = async () => {
    const { collectionId } = deleteConfirmModal;
    
    setDeletingCollectionId(collectionId);
    try {
      await medusaAdminService.deleteCollection(dynamicStoreHandle, collectionId);
      showToast('Collection deleted successfully', 'success');
      
      // Remove the deleted collection from the local state
      setCollections(prev => prev.filter(collection => collection.id !== collectionId));
      setSelectedCollections(prev => prev.filter(id => id !== collectionId));
      setTotalCount(prev => prev - 1);
      
      // Close the modal
      setDeleteConfirmModal({ isOpen: false, collectionId: '', collectionName: '' });
    } catch (error: any) {
      console.error('Error deleting collection:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete collection';
      showToast(errorMessage, 'error');
    } finally {
      setDeletingCollectionId(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmModal({ isOpen: false, collectionId: '', collectionName: '' });
  };



  return (
    <Stack spacing={3} sx={{ p: 3 }}>
    {/* Page Header */}
    <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
      <Box>
        <Typography variant="h4" component="h1" gutterBottom>
          Collections
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Organize products into collections
        </Typography>
      </Box>
      <Stack direction="row" spacing={2}>
        <Button variant="outlined">
          Export
        </Button>
        <Button
          onClick={() => navigateWithLoading(`/${dynamicStoreHandle}/admin/collections/new`)}
          variant="contained"
          startIcon={<Add />}
          size="large"
        >
          Add Collection
        </Button>
      </Stack>
    </Stack>
  
    {/* Search */}
    <Card>
      <CardContent>
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={2}
          alignItems={{ xs: 'stretch', sm: 'center' }}
          justifyContent="space-between"
        >
          <TextField
            placeholder="Search collections..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            size="small"
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }
            }}
            sx={{ minWidth: 250 }}
          />
  
          <Stack className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              {totalCount > 0 ? `${totalCount} total collections` : `${filteredCollections.length} collections`}
            </span>
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  
    {/* Bulk Actions */}
    {selectedCollections.length > 0 && (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-blue-900">
            {selectedCollections.length} collection(s) selected
          </span>
          <div className="flex items-center space-x-2">
            <button className="px-3 py-1 text-sm bg-white border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors">
              Bulk Edit
            </button>
            <button className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
              Delete
            </button>
          </div>
        </div>
      </div>
    )}
  
    {/* Error Display */}
    {error && (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-red-700">
              <span className="font-medium">Error loading collections:</span> {error}
            </p>
          </div>
          <div className="ml-auto">
            <button
              onClick={() => window.location.reload()}
              className="text-sm text-red-600 hover:text-red-700 font-medium"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    )}
  
    {/* Collections Table */}
    {isLoading ? (
      <AdminTableLoading />
    ) : error ? null : (
      <TableContainer component={Paper}>
        {filteredCollections.length > 0 ? (
          <>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedCollections.length === filteredCollections.length && filteredCollections.length > 0}
                      onChange={handleSelectAll}
                      indeterminate={selectedCollections.length > 0 && selectedCollections.length < filteredCollections.length}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                      Collection
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                      Handle
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                      Products
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                      Created
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                      Actions
                    </Typography>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredCollections.map((collection) => {
                  const collectionId = collection.id;
                  const collectionName = collection.title || 'Unnamed Collection';
                  const collectionHandle = collection.handle || 'N/A';
                  const collectionProductCount = 0; // Medusa doesn't return product count in basic response
                  const collectionCreatedAt = collection.created_at;
  
                  return (
                    <TableRow key={collectionId} hover>
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={selectedCollections.includes(collectionId)}
                          onChange={() => handleSelectCollection(collectionId)}
                        />
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Box sx={{
                            width: 40,
                            height: 40,
                            bgcolor: 'primary.100',
                            borderRadius: 2,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            <Collections sx={{ fontSize: 20, color: 'primary.main' }} />
                          </Box>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {collectionName}
                          </Typography>
                        </Stack>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{collectionHandle}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{collectionProductCount}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {collectionCreatedAt ? (
                            new Date(collectionCreatedAt).toLocaleDateString()
                          ) : (
                            'Date not available'
                          )}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                          <IconButton
                            onClick={() => navigateWithLoading(`/${dynamicStoreHandle}/admin/collections/${collectionId}`)}
                            size="small"
                            color="primary"
                          >
                            <Edit />
                          </IconButton>
                          <IconButton
                            onClick={() => handleDeleteClick(collectionId, collectionName)}
                            disabled={deletingCollectionId === collectionId}
                            size="small"
                            color="error"
                          >
                            {deletingCollectionId === collectionId ? <CircularProgress size={16} /> : <Delete />}
                          </IconButton>
                        </Stack>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
  
            {/* Pagination - Only show when there are collections */}
            <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  Showing 1 to {filteredCollections.length} of {filteredCollections.length} results
                </Typography>
                <Pagination
                  count={1}
                  page={1}
                  size="small"
                  showFirstButton
                  showLastButton
                />
              </Stack>
            </Box>
          </>
        ) : (
          /* No Collections Message */
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Box sx={{
              mx: 'auto',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 64,
              height: 64,
              borderRadius: '50%',
              bgcolor: 'grey.100',
              mb: 2
            }}>
              <Collections sx={{ fontSize: 32, color: 'text.secondary' }} />
            </Box>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
              {searchTerm ? 'No collections found' : 'No collections available'}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              {searchTerm
                ? 'Try adjusting your search criteria to find collections.'
                : 'Get started by creating your first product collection.'
              }
            </Typography>
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center">
              {searchTerm && (
                <Button
                  variant="outlined"
                  onClick={() => setSearchTerm('')}
                >
                  Clear Search
                </Button>
              )}
              <Button
                onClick={() => navigateWithLoading(`/${dynamicStoreHandle}/admin/collections/new`)}
                variant="contained"
                startIcon={<Add />}
              >
                Add Your First Collection
              </Button>
            </Stack>
          </Box>
        )}
      </TableContainer>
    )}
  
    {/* Delete Confirmation Modal */}
    <ConfirmationModal
      isOpen={deleteConfirmModal.isOpen}
      onClose={handleDeleteCancel}
      onConfirm={handleDeleteConfirm}
      title="Delete Collection"
      message={`Are you sure you want to delete the collection "${deleteConfirmModal.collectionName}"? This action cannot be undone.`}
      confirmText="Delete"
      cancelText="Cancel"
      confirmButtonColor="red"
      icon="danger"
      isLoading={deletingCollectionId === deleteConfirmModal.collectionId}
    />
  </Stack>
  );
};