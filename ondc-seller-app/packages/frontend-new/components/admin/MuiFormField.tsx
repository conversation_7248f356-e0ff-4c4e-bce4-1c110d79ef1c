'use client';

import React from 'react';
import {
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Box,
  Typography,
} from '@mui/material';

interface Option {
  value: string;
  label: string;
}

interface MuiFormFieldProps {
  label: string;
  name: string;
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  type?: 'text' | 'number' | 'email' | 'password' | 'textarea' | 'select';
  placeholder?: string;
  required?: boolean;
  help?: string;
  error?: string;
  disabled?: boolean;
  rows?: number;
  options?: Option[];
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
  multiline?: boolean;
  fullWidth?: boolean;
}

const MuiFormField: React.FC<MuiFormFieldProps> = ({
  label,
  name,
  value,
  onChange,
  type = 'text',
  placeholder,
  required = false,
  help,
  error,
  disabled = false,
  rows = 4,
  options = [],
  startAdornment,
  endAdornment,
  multiline,
  fullWidth = true,
}) => {
  // Handle select field changes
  const handleSelectChange = (event: any) => {
    // Create a synthetic event that matches the expected interface
    const syntheticEvent = {
      target: {
        name,
        value: event.target.value,
      },
    } as React.ChangeEvent<HTMLInputElement>;
    onChange(syntheticEvent);
  };

  // Determine if field has error
  const hasError = Boolean(error);

  // For select fields
  if (type === 'select') {
    return (
      <Box sx={{ mb: 2 }}>
        <FormControl fullWidth={fullWidth} error={hasError} required={required} disabled={disabled}>
          <InputLabel id={`${name}-label`}>{label}</InputLabel>
          <Select
            labelId={`${name}-label`}
            id={name}
            name={name}
            value={value || ''}
            label={label}
            onChange={handleSelectChange}
            startAdornment={startAdornment}
            endAdornment={endAdornment}
          >
            {placeholder && (
              <MenuItem value="" disabled>
                <em>{placeholder}</em>
              </MenuItem>
            )}
            {options.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
          {(error || help) && (
            <FormHelperText>
              {error || help}
            </FormHelperText>
          )}
        </FormControl>
      </Box>
    );
  }

  // For all other field types
  return (
    <Box sx={{ mb: 2 }}>
      <TextField
        fullWidth={fullWidth}
        label={label}
        name={name}
        value={value || ''}
        onChange={onChange}
        type={type === 'textarea' ? 'text' : type}
        placeholder={placeholder}
        required={required}
        error={hasError}
        helperText={error || help}
        disabled={disabled}
        multiline={type === 'textarea' || multiline}
        rows={type === 'textarea' ? rows : undefined}
        InputProps={{
          startAdornment,
          endAdornment,
        }}
        variant="outlined"
      />
    </Box>
  );
};

export default MuiFormField;