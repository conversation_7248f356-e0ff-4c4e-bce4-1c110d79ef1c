'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Chip,
  Tooltip,
} from '@mui/material';
import {
  AccountCircle,
  Logout,
  Store,
  Settings,
  Notifications,
  PersonAdd,
} from '@mui/icons-material';
import { useAuthStore } from '@/stores/authStore';
import { performAdminLogout } from '@/lib/utils/logout';

interface AdminHeaderProps {
  storeHandle: string;
}

export const AdminHeader: React.FC<AdminHeaderProps> = ({ storeHandle }) => {
  const router = useRouter();
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const { user, getStoreHandle, clearAuth } = useAuthStore();
  
  // Handle null user state during logout process
  const isUserNull = !user;
  
  // Extract dynamic data from auth store with proper null checking
  const userName = user?.user ? `${user.user?.first_name || ''} ${user.user?.last_name || ''}`.trim() : 'Admin User';
  const userEmail = user?.user?.email || '<EMAIL>';
  const dynamicStoreHandle = getStoreHandle() || storeHandle;
  const storeName = user?.user?.metadata?.store_name ||
                   user?.metadata?.store_name ||
                   (storeHandle ? storeHandle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Store Admin');
  
  // Get user initials for avatar
  const userInitials = userName.split(' ').map(n => n[0]).join('').toUpperCase() || 'AU';
  
  // Get user profile image from metadata
  const userProfileImage = user?.user?.metadata?.profile_image || 
                          user?.metadata?.profile_image || 
                          null;
  

  const handleLogout = () => {
    // Close profile dropdown first
    setIsProfileOpen(false);
    
    // Perform complete admin logout using utility function with store handle
    performAdminLogout(router, storeHandle);
  };
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };


  // Don't render content if user is null (during logout)
  if (isUserNull) {
    return (
      <AppBar
        position="fixed"
        sx={{
          bgcolor: 'background.paper',
          color: 'text.primary',
          boxShadow: 1,
          borderBottom: 1,
          borderColor: 'divider',
          height: 80,
          zIndex: (theme) => theme.zIndex.drawer + 1,
        }}
      >
        <Toolbar sx={{ height: 80, px: 3 }}>
          {/* Empty toolbar during logout */}
        </Toolbar>
      </AppBar>
    );
  }

  return (
    <AppBar
      position="fixed"
      sx={{
        bgcolor: 'background.paper',
        color: 'text.primary',
        boxShadow: 1,
        borderBottom: 1,
        borderColor: 'divider',
        height: 80,
        zIndex: (theme) => theme.zIndex.drawer + 1,
      }}
    >
      <Toolbar sx={{ height: 80, px: 3 }}>
        {/* Logo and Title */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box
            component={Link}
            href={`/${dynamicStoreHandle}/admin`}
            sx={{ display: 'flex', alignItems: 'center', gap: 2, textDecoration: 'none', color: 'inherit' }}
          >
            <Avatar sx={{
              width: 32,
              height: 32,
              background: 'linear-gradient(to right, #2563eb, #9333ea)',
              fontSize: '0.875rem',
              fontWeight: 'bold'
            }}>
              {storeName.charAt(0).toUpperCase()}
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 'bold', lineHeight: 1.2 }}>
                {storeName}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Admin Dashboard • {dynamicStoreHandle}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Spacer */}
        <Box sx={{ flexGrow: 1 }} />

        {/* Right Side Actions */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* Notifications */}
          <IconButton color="inherit" sx={{ position: 'relative' }}>
            <Notifications />
            <Box sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              width: 8,
              height: 8,
              bgcolor: 'error.main',
              borderRadius: '50%'
            }} />
          </IconButton>

          {/* Profile Dropdown */}
          <Box>
            <IconButton
              onClick={(e) => setIsProfileOpen(Boolean(e.currentTarget))}
              sx={{ p: 1 }}
            >

              <Box sx={{ display: 'flex', alignItems: 'center', textAlign: 'center' }}>
       
              <Tooltip title="Account settings">
                <IconButton
                  onClick={handleClick}
                  size="small"
                  // sx={{ ml: 2 }}
                  aria-controls={open ? 'account-menu' : undefined}
                  aria-haspopup="true"
                  aria-expanded={open ? 'true' : undefined}
                >
                  <Avatar 
                    src={userProfileImage || undefined}
                    sx={{ width: 32, height: 32 }}
                  >
                    {!userProfileImage && (user?.user?.first_name?.charAt(0)?.toUpperCase() || 'A')}
                  </Avatar>
                </IconButton>
              </Tooltip>
            </Box>
            <Menu
              anchorEl={anchorEl}
              id="account-menu"
              open={open}
              onClose={handleClose}
              onClick={handleClose}
              slotProps={{
                paper: {
                  elevation: 0,
                  sx: {
                    overflow: 'visible',
                    filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                    mt: 1.5,
                    '& .MuiAvatar-root': {
                      width: 32,
                      height: 32,
                      ml: -0.5,
                      mr: 1,
                    },
                    '&::before': {
                      content: '""',
                      display: 'block',
                      position: 'absolute',
                      top: 0,
                      right: 14,
                      width: 10,
                      height: 10,
                      bgcolor: 'background.paper',
                      transform: 'translateY(-50%) rotate(45deg)',
                      zIndex: 0,
                    },
                  },
                },
              }}
              transformOrigin={{ horizontal: 'right', vertical: 'top' }}
              anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
              <Box sx={{ px: 2, py: 1.5, borderBottom: 1, borderColor: 'divider' }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  {userName}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {userEmail}
                </Typography>
                <Chip
                  label={storeName}
                  size="small"
                  color="primary"
                  variant="outlined"
                  sx={{ mt: 0.5, fontSize: '0.75rem' }}
                />
              </Box>
              <MenuItem component={Link} href={`/${dynamicStoreHandle}/admin/profile`} onClick={() => setIsProfileOpen(false)}>
                <Avatar 
                  src={userProfileImage || undefined}
                  sx={{ width: 24, height: 24, mr: 1 }}
                >
                  {!userProfileImage && userInitials}
                </Avatar>
                Profile Settings
              </MenuItem>
              <MenuItem component={Link} href={`/${dynamicStoreHandle}/admin/store-settings`} onClick={() => setIsProfileOpen(false)}>
                <ListItemIcon>
                  <Settings fontSize="small" />
                </ListItemIcon>
                Store Settings
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleLogout}>
                <ListItemIcon>
                  <Logout fontSize="small" />
                </ListItemIcon>
                Logout
              </MenuItem>
            </Menu>
            </IconButton>       
          </Box>
        </Box>
      </Toolbar>
    </AppBar>
  );
};