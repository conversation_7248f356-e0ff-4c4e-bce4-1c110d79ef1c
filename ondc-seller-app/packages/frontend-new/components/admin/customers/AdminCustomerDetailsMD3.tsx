'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useNavigationWithLoading } from '@/hooks/useNavigationWithLoading';
import Link from 'next/link';
import { useAuthStore } from '@/stores/authStore';
import { customersApi, Customer } from '@/lib/api/customers';
import { useToast } from '@/app/providers/toast-provider';

// Material Design 3 Components
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  IconButton,
  Stack,
  Divider,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Breadcrumbs,
  Link as MuiLink,
  Avatar,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
  CircularProgress,
  Container,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Skeleton,
} from '@mui/material';

import {
  ArrowBack,
  Edit as EditIcon,
  Block as BlockIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  ShoppingBag as OrdersIcon,
  MoreVert as MoreVertIcon,
  Person as PersonIcon,
  NavigateNext as NavigateNextIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  History as HistoryIcon,
  Receipt as ReceiptIcon,
  CreditCard as PaymentIcon,
  LocalShipping as ShippingIcon,
} from '@mui/icons-material';

interface AdminCustomerDetailsMD3Props {
  storeHandle: string;
  customerId: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`customer-tabpanel-${index}`}
      aria-labelledby={`customer-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export const AdminCustomerDetailsMD3: React.FC<AdminCustomerDetailsMD3Props> = ({ 
  storeHandle, 
  customerId 
}) => {
  const router = useRouter();
  const { getStoreHandle } = useAuthStore();
  const { showToast } = useToast();
  const { navigateWithLoading } = useNavigationWithLoading();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [editDialog, setEditDialog] = useState(false);
  const [blockDialog, setBlockDialog] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [editFormData, setEditFormData] = useState<Partial<Customer>>({});

  // Get dynamic store handle from auth store
  const dynamicStoreHandle = getStoreHandle() || storeHandle;

  // Mock data for orders and addresses (replace with actual API calls)
  const mockOrders = [
    { id: 'ORD-001', total: 2499, status: 'completed', date: '2024-01-15', items: 3 },
    { id: 'ORD-002', total: 1899, status: 'processing', date: '2024-01-10', items: 2 },
    { id: 'ORD-003', total: 3299, status: 'shipped', date: '2024-01-05', items: 5 },
  ];

  const mockAddresses = [
    {
      id: 'addr-1',
      type: 'shipping',
      first_name: 'John',
      last_name: 'Doe',
      address_1: '123 Main Street',
      city: 'Mumbai',
      postal_code: '400001',
      country: 'India',
      phone: '+91 9876543210'
    },
    {
      id: 'addr-2',
      type: 'billing',
      first_name: 'John',
      last_name: 'Doe',
      address_1: '456 Business Ave',
      city: 'Delhi',
      postal_code: '110001',
      country: 'India',
      phone: '+91 9876543210'
    }
  ];

  // Fetch customer details
  useEffect(() => {
    const fetchCustomerDetails = async () => {
      if (!dynamicStoreHandle || !customerId) {
        console.log('Missing store handle or customer ID');
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        console.log('=== FETCHING CUSTOMER DETAILS ===');
        console.log('Store handle:', dynamicStoreHandle);
        console.log('Customer ID:', customerId);

        const customerData = await customersApi.getCustomer(dynamicStoreHandle, customerId);
        console.log('Customer details fetched successfully:', customerData);
        
        setCustomer(customerData);
        setEditFormData(customerData);
      } catch (error: any) {
        console.error('Failed to fetch customer details:', error);
        setError(error.message || 'Failed to fetch customer details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomerDetails();
  }, [dynamicStoreHandle, customerId]);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEditCustomer = () => {
    setEditDialog(true);
    handleMenuClose();
  };

  const handleBlockCustomer = () => {
    setBlockDialog(true);
    handleMenuClose();
  };

  const handleUpdateCustomer = async () => {
    if (!customer || !editFormData) return;

    setIsUpdating(true);
    try {
      const updatedCustomer = await customersApi.updateCustomer(
        dynamicStoreHandle, 
        customerId, 
        editFormData
      );
      
      setCustomer(updatedCustomer);
      setEditDialog(false);
      showToast('Customer updated successfully', 'success');
    } catch (error: any) {
      console.error('Failed to update customer:', error);
      showToast('Failed to update customer', 'error');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  const getStatusColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'blocked':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(amount);
  };

  const getCustomerInitials = (customer: Customer): string => {
    const firstName = customer.first_name || '';
    const lastName = customer.last_name || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || customer.email.charAt(0).toUpperCase();
  };

  const getOrderStatusColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'info';
      case 'shipped':
        return 'primary';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  if (isLoading) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Stack spacing={3}>
          <Skeleton variant="text" width={200} height={40} />
          <Skeleton variant="text" width={300} height={24} />
          <Card elevation={1}>
            <CardContent>
              <Stack spacing={3}>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Skeleton variant="circular" width={80} height={80} />
                  <Box sx={{ flexGrow: 1 }}>
                    <Skeleton variant="text" width={200} height={32} />
                    <Skeleton variant="text" width={150} height={24} />
                  </Box>
                </Stack>
                <Skeleton variant="rectangular" width="100%" height={200} />
              </Stack>
            </CardContent>
          </Card>
        </Stack>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Alert 
          severity="error" 
          action={
            <Button color="inherit" size="small" onClick={handleRefresh} startIcon={<RefreshIcon />}>
              Retry
            </Button>
          }
        >
          <strong>Error loading customer:</strong> {error}
        </Alert>
      </Container>
    );
  }

  if (!customer) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Alert severity="warning">
          Customer not found. The customer may have been deleted or you may not have permission to view it.
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />}
        sx={{ mb: 3 }}
      >
        <MuiLink 
          component={Link} 
          href={`/${dynamicStoreHandle}/admin`}
          underline="hover"
          color="inherit"
        >
          Admin
        </MuiLink>
        <MuiLink 
          component={Link} 
          href={`/${dynamicStoreHandle}/admin/customers`}
          underline="hover"
          color="inherit"
        >
          Customers
        </MuiLink>
        <Typography color="text.primary">
          {customer.first_name && customer.last_name 
            ? `${customer.first_name} ${customer.last_name}`
            : customer.email
          }
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ mb: 3 }}>
        <Box>
          <Button
            startIcon={<ArrowBack />}
            onClick={() => navigateWithLoading(`/${dynamicStoreHandle}/admin/customers`)}
            sx={{ mb: 2 }}
            color="inherit"
          >
            Back to Customers
          </Button>
          
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Customer Details
          </Typography>
          
          <Typography variant="body1" color="text.secondary">
            Manage customer information and view order history
          </Typography>
        </Box>

        <Stack direction="row" spacing={2}>
          <Button variant="outlined" startIcon={<EmailIcon />}>
            Send Email
          </Button>
          <IconButton onClick={handleMenuOpen}>
            <MoreVertIcon />
          </IconButton>
        </Stack>
      </Stack>

      {/* Customer Overview Card */}
      <Card elevation={1} sx={{ mb: 3 }}>
        <CardContent sx={{ p: 4 }}>
          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Stack direction="row" spacing={3} alignItems="center">
                <Avatar
                  sx={{ 
                    width: 80, 
                    height: 80,
                    bgcolor: 'primary.main',
                    fontSize: '2rem',
                    fontWeight: 600,
                  }}
                >
                  {getCustomerInitials(customer)}
                </Avatar>
                
                <Box>
                  <Typography variant="h5" fontWeight="bold" gutterBottom>
                    {customer.first_name && customer.last_name 
                      ? `${customer.first_name} ${customer.last_name}`
                      : customer.email
                    }
                  </Typography>
                  <Typography variant="body1" color="text.secondary" gutterBottom>
                    {customer.email}
                  </Typography>
                  <Chip
                    label={customer.status?.charAt(0).toUpperCase() + customer.status?.slice(1) || 'Active'}
                    color={getStatusColor(customer.status)}
                    size="small"
                  />
                </Box>
              </Stack>
            </Grid>
            
            <Grid item xs={12} md={8}>
              <Grid container spacing={3}>
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary" fontWeight="bold">
                      {customer.total_orders || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Orders
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="success.main" fontWeight="bold">
                      {formatCurrency(customer.total_spent || 0)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Spent
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="info.main" fontWeight="bold">
                      {customer.total_spent && customer.total_orders 
                        ? formatCurrency((customer.total_spent / customer.total_orders))
                        : '₹0'
                      }
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Avg Order Value
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6} sm={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="warning.main" fontWeight="bold">
                      {customer.last_order_date 
                        ? new Date(customer.last_order_date).toLocaleDateString('en-IN', { day: 'numeric', month: 'short' })
                        : 'Never'
                      }
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Last Order
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Card elevation={1}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={(_, newValue) => setTabValue(newValue)}
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab 
              label="Overview" 
              icon={<PersonIcon />} 
              iconPosition="start"
            />
            <Tab 
              label="Orders" 
              icon={<OrdersIcon />} 
              iconPosition="start"
            />
            <Tab 
              label="Addresses" 
              icon={<LocationIcon />} 
              iconPosition="start"
            />
            <Tab 
              label="Activity" 
              icon={<HistoryIcon />} 
              iconPosition="start"
            />
          </Tabs>
        </Box>

        {/* Overview Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom fontWeight={600}>
                    Contact Information
                  </Typography>
                  
                  <List>
                    <ListItem>
                      <ListItemIcon>
                        <EmailIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary="Email"
                        secondary={customer.email}
                      />
                    </ListItem>
                    
                    {customer.phone && (
                      <ListItem>
                        <ListItemIcon>
                          <PhoneIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary="Phone"
                          secondary={customer.phone}
                        />
                      </ListItem>
                    )}
                    
                    <ListItem>
                      <ListItemIcon>
                        <PersonIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary="Customer ID"
                        secondary={customer.id}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom fontWeight={600}>
                    Account Details
                  </Typography>
                  
                  <List>
                    <ListItem>
                      <ListItemText
                        primary="Status"
                        secondary={
                          <Chip
                            label={customer.status?.charAt(0).toUpperCase() + customer.status?.slice(1) || 'Active'}
                            color={getStatusColor(customer.status)}
                            size="small"
                          />
                        }
                      />
                    </ListItem>
                    
                    <ListItem>
                      <ListItemText
                        primary="Member Since"
                        secondary={new Date(customer.createdAt).toLocaleDateString('en-IN', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}
                      />
                    </ListItem>
                    
                    {customer.updatedAt && (
                      <ListItem>
                        <ListItemText
                          primary="Last Updated"
                          secondary={new Date(customer.updatedAt).toLocaleDateString('en-IN', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                          })}
                        />
                      </ListItem>
                    )}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Orders Tab */}
        <TabPanel value={tabValue} index={1}>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Order ID</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Items</TableCell>
                  <TableCell>Total</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {mockOrders.map((order) => (
                  <TableRow key={order.id} hover>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight={600}>
                        {order.id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      {new Date(order.date).toLocaleDateString('en-IN')}
                    </TableCell>
                    <TableCell>{order.items}</TableCell>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight={600}>
                        {formatCurrency(order.total)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        color={getOrderStatusColor(order.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Button
                        component={Link}
                        href={`/${dynamicStoreHandle}/admin/orders/${order.id}`}
                        size="small"
                        variant="outlined"
                        startIcon={<ReceiptIcon />}
                      >
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Addresses Tab */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            {mockAddresses.map((address) => (
              <Grid item xs={12} md={6} key={address.id}>
                <Card variant="outlined">
                  <CardContent>
                    <Stack direction="row" justifyContent="space-between" alignItems="flex-start" mb={2}>
                      <Typography variant="h6" fontWeight={600}>
                        {address.type === 'shipping' ? 'Shipping Address' : 'Billing Address'}
                      </Typography>
                      <IconButton size="small">
                        <EditIcon />
                      </IconButton>
                    </Stack>
                    
                    <Typography variant="body1" fontWeight={500}>
                      {address.first_name} {address.last_name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {address.address_1}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {address.city}, {address.postal_code}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {address.country}
                    </Typography>
                    {address.phone && (
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        {address.phone}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Activity Tab */}
        <TabPanel value={tabValue} index={3}>
          <Typography variant="body1" color="text.secondary">
            Customer activity timeline will be displayed here.
          </Typography>
        </TabPanel>
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleEditCustomer}>
          <EditIcon sx={{ mr: 1 }} />
          Edit Customer
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <EmailIcon sx={{ mr: 1 }} />
          Send Email
        </MenuItem>
        <MenuItem onClick={handleBlockCustomer} sx={{ color: 'error.main' }}>
          <BlockIcon sx={{ mr: 1 }} />
          Block Customer
        </MenuItem>
      </Menu>

      {/* Edit Customer Dialog */}
      <Dialog open={editDialog} onClose={() => setEditDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Customer</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 2 }}>
            <TextField
              label="First Name"
              value={editFormData.first_name || ''}
              onChange={(e) => setEditFormData({ ...editFormData, first_name: e.target.value })}
              fullWidth
            />
            <TextField
              label="Last Name"
              value={editFormData.last_name || ''}
              onChange={(e) => setEditFormData({ ...editFormData, last_name: e.target.value })}
              fullWidth
            />
            <TextField
              label="Email"
              value={editFormData.email || ''}
              onChange={(e) => setEditFormData({ ...editFormData, email: e.target.value })}
              fullWidth
              type="email"
            />
            <TextField
              label="Phone"
              value={editFormData.phone || ''}
              onChange={(e) => setEditFormData({ ...editFormData, phone: e.target.value })}
              fullWidth
            />
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={editFormData.status || 'active'}
                onChange={(e) => setEditFormData({ ...editFormData, status: e.target.value as any })}
                label="Status"
              >
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
                <MenuItem value="blocked">Blocked</MenuItem>
              </Select>
            </FormControl>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleUpdateCustomer} 
            variant="contained"
            disabled={isUpdating}
            startIcon={isUpdating ? <CircularProgress size={16} /> : <SaveIcon />}
          >
            {isUpdating ? 'Updating...' : 'Update Customer'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Block Customer Dialog */}
      <Dialog open={blockDialog} onClose={() => setBlockDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Block Customer</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to block this customer? They will not be able to place new orders.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBlockDialog(false)}>Cancel</Button>
          <Button 
            onClick={() => setBlockDialog(false)} 
            variant="contained"
            color="error"
          >
            Block Customer
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};