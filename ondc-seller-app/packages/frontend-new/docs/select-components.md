# Select Components Documentation

This document provides comprehensive documentation for the reusable Material Design 3 select components.

## Overview

The select components are built on top of Material-UI v7 and follow Material Design 3 principles. They provide a consistent, accessible, and customizable way to handle single and multiple selections in forms.

## Components

### 1. Select (Base Component)

The base select component that supports both single and multiple selection modes.

```tsx
import { Select, SelectOption } from '@/components/ui';

const options: SelectOption[] = [
  { value: 'option1', label: 'Option 1', description: 'Description for option 1' },
  { value: 'option2', label: 'Option 2', disabled: true },
  { value: 'option3', label: 'Option 3' },
];

<Select
  label="Choose an option"
  placeholder="Select..."
  value={value}
  onChange={setValue}
  options={options}
  multiple={false}
  required
  helperText="Please select an option"
/>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | - | Label for the select field |
| `placeholder` | `string` | - | Placeholder text when no option is selected |
| `value` | `string \| number \| string[] \| number[]` | `''` or `[]` | Current selected value(s) |
| `options` | `SelectOption[]` | `[]` | Array of options to display |
| `onChange` | `function` | - | Callback when selection changes |
| `multiple` | `boolean` | `false` | Enable multiple selection |
| `required` | `boolean` | `false` | Mark field as required |
| `disabled` | `boolean` | `false` | Disable the select |
| `error` | `boolean` | `false` | Show error state |
| `helperText` | `string` | - | Helper text below the select |
| `fullWidth` | `boolean` | `true` | Take full width of container |
| `size` | `'small' \| 'medium'` | `'medium'` | Size of the select |
| `variant` | `'outlined' \| 'filled' \| 'standard'` | `'outlined'` | Visual variant |

### 2. SingleSelect

A specialized component for single selection with additional features like clearable option.

```tsx
import { SingleSelect } from '@/components/ui';

<SingleSelect
  label="Country"
  placeholder="Select a country"
  value={selectedCountry}
  onChange={setSelectedCountry}
  options={countries}
  clearable
  helperText="Choose your country"
/>
```

#### Additional Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `clearable` | `boolean` | `false` | Show clear button when value is selected |
| `clearIcon` | `React.ReactNode` | `<ClearIcon />` | Custom clear icon |
| `onClear` | `function` | - | Callback when clear button is clicked |

### 3. MultiSelect

A specialized component for multiple selection with chip display and advanced features.

```tsx
import { MultiSelect } from '@/components/ui';

<MultiSelect
  label="Skills"
  placeholder="Select your skills"
  value={selectedSkills}
  onChange={setSelectedSkills}
  options={skills}
  showSelectAll
  maxTags={3}
  chipVariant="filled"
  chipColor="primary"
/>
```

#### Additional Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `maxTags` | `number` | - | Maximum number of chips to display before showing "+X more" |
| `showSelectAll` | `boolean` | `false` | Show "Select All" option |
| `selectAllText` | `string` | `'Select All'` | Text for select all option |
| `chipVariant` | `'filled' \| 'outlined'` | `'filled'` | Variant for selected chips |
| `chipColor` | MUI chip colors | `'primary'` | Color for selected chips |

## Form Integration

### React Hook Form Integration

The components come with built-in React Hook Form integration:

```tsx
import { useForm } from 'react-hook-form';
import { FormSingleSelect, FormMultiSelect } from '@/components/ui';

const form = useForm({
  defaultValues: {
    country: '',
    skills: [],
  },
});

<FormSingleSelect
  name="country"
  control={form.control}
  label="Country"
  options={countries}
  rules={{ required: 'Country is required' }}
/>

<FormMultiSelect
  name="skills"
  control={form.control}
  label="Skills"
  options={skills}
  rules={{ 
    required: 'At least one skill is required',
    validate: (value) => value.length <= 5 || 'Maximum 5 skills allowed'
  }}
/>
```

### Validation with Zod

```tsx
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

const schema = z.object({
  country: z.string().min(1, 'Country is required'),
  skills: z.array(z.string())
    .min(1, 'At least one skill is required')
    .max(5, 'Maximum 5 skills allowed'),
});

const form = useForm({
  resolver: zodResolver(schema),
});
```

## SelectOption Interface

```tsx
interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  description?: string;
}
```

## Styling and Theming

The components automatically inherit the Material-UI theme and support custom styling:

```tsx
<Select
  sx={{
    '& .MuiOutlinedInput-root': {
      borderRadius: 3,
    },
  }}
  // ... other props
/>
```

## Accessibility

All components include proper ARIA attributes and keyboard navigation:

- Full keyboard navigation support
- Screen reader compatibility
- Proper focus management
- ARIA labels and descriptions

## Examples

### Basic Usage

```tsx
const [country, setCountry] = useState('');
const [skills, setSkills] = useState([]);

const countries = [
  { value: 'us', label: 'United States' },
  { value: 'ca', label: 'Canada' },
  { value: 'uk', label: 'United Kingdom' },
];

return (
  <>
    <SingleSelect
      label="Country"
      value={country}
      onChange={setCountry}
      options={countries}
      clearable
    />
    
    <MultiSelect
      label="Skills"
      value={skills}
      onChange={setSkills}
      options={skillOptions}
      showSelectAll
      maxTags={2}
    />
  </>
);
```

### With Validation

```tsx
const form = useForm({
  resolver: zodResolver(schema),
});

return (
  <form onSubmit={form.handleSubmit(onSubmit)}>
    <FormSingleSelect
      name="country"
      control={form.control}
      label="Country *"
      options={countries}
      rules={{ required: 'Country is required' }}
    />
    
    <FormMultiSelect
      name="skills"
      control={form.control}
      label="Skills *"
      options={skills}
      showSelectAll
      rules={{
        required: 'At least one skill is required',
        validate: (value) => value.length <= 5 || 'Max 5 skills'
      }}
    />
    
    <Button type="submit">Submit</Button>
  </form>
);
```

## Best Practices

1. **Use appropriate component**: Use `SingleSelect` for single selection and `MultiSelect` for multiple selection
2. **Provide clear labels**: Always include descriptive labels for accessibility
3. **Add helper text**: Use helper text to guide users
4. **Handle loading states**: Show loading indicators when fetching options
5. **Validate input**: Always validate form inputs on both client and server
6. **Limit options**: For large datasets, consider pagination or search functionality
7. **Use descriptions**: Add descriptions to options when helpful
8. **Test accessibility**: Ensure components work with screen readers and keyboard navigation

## Migration from Existing Components

If you're migrating from existing select components:

1. Replace `<select>` elements with `<SingleSelect>`
2. Replace multiple select implementations with `<MultiSelect>`
3. Update form integration to use `FormSingleSelect` or `FormMultiSelect`
4. Update option data structure to match `SelectOption` interface
5. Test all functionality and accessibility features

## Performance Considerations

- Options are virtualized for large datasets
- Chips in MultiSelect are optimized for rendering
- Components use React.memo for performance optimization
- Debounced search functionality (when implemented)

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Troubleshooting

### Common Issues

1. **Options not displaying**: Check that options array is properly formatted
2. **Value not updating**: Ensure onChange handler is properly connected
3. **Validation not working**: Verify form integration and validation rules
4. **Styling issues**: Check theme configuration and custom styles
5. **Accessibility warnings**: Ensure proper labels and ARIA attributes

### Debug Mode

Enable debug mode to see component state:

```tsx
<Select
  // ... props
  sx={{ '&[data-debug="true"]': { border: '2px solid red' } }}
  data-debug={process.env.NODE_ENV === 'development'}
/>
```