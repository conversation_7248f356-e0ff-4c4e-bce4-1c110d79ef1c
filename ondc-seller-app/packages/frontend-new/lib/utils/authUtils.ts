'use client';

import { useAuthStore } from '@/stores/authStore';
import { clearStoreAuthStore } from '@/stores/storeAuthStore';

/**
 * Centralized function to clear all authentication data
 * This should be called on 401 errors or manual logout
 */
export const clearAllAuthData = (storeHandle?: string) => {
  console.log('🧹 Starting complete auth data clearing...');
  
  try {
    // 1. Clear main auth store (Zustand)
    console.log('🧹 Clearing main auth store...');
    const authStore = useAuthStore.getState();
    authStore.clearAuth();
    
    // 2. Clear store-specific auth store if storeHandle provided
    if (storeHandle) {
      console.log(`🧹 Clearing store-specific auth for: ${storeHandle}`);
      clearStoreAuthStore(storeHandle);
    }
    
    // 3. Clear localStorage items
    if (typeof window !== 'undefined') {
      console.log('🧹 Clearing localStorage items...');
      
      // Clear main auth storage
      localStorage.removeItem('auth-storage');
      localStorage.removeItem('ondc_auth_token');
      
      // Clear store-specific auth if storeHandle provided
      if (storeHandle) {
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.includes(storeHandle) || key.includes(`${storeHandle}-auth`))) {
            keysToRemove.push(key);
          }
        }
        
        keysToRemove.forEach(key => {
          localStorage.removeItem(key);
          console.log(`🧹 Removed localStorage key: ${key}`);
        });
      }
      
      // Clear any other auth-related items
      const authRelatedKeys = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('auth') || key.includes('token'))) {
          authRelatedKeys.push(key);
        }
      }
      
      authRelatedKeys.forEach(key => {
        localStorage.removeItem(key);
        console.log(`🧹 Removed auth-related key: ${key}`);
      });
    }
    
    // 4. Clear any session storage items
    if (typeof window !== 'undefined' && window.sessionStorage) {
      console.log('🧹 Clearing sessionStorage items...');
      const sessionKeys = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (key.includes('auth') || key.includes('token'))) {
          sessionKeys.push(key);
        }
      }
      
      sessionKeys.forEach(key => {
        sessionStorage.removeItem(key);
        console.log(`🧹 Removed sessionStorage key: ${key}`);
      });
    }
    
    console.log('✅ Complete auth data clearing finished');
    
  } catch (error) {
    console.error('❌ Error during auth data clearing:', error);
  }
};

/**
 * Handle 401 unauthorized response with complete cleanup and redirect
 */
export const handle401Unauthorized = (storeHandle?: string, redirectPath?: string) => {
  console.log('🚨 401 Unauthorized detected - starting cleanup and redirect');
  
  // Clear all auth data
  clearAllAuthData(storeHandle);
  
  // Redirect to login
  if (typeof window !== 'undefined') {
    const loginUrl = redirectPath || '/login?tab=login';
    console.log(`🔄 Redirecting to: ${loginUrl}`);
    
    // Use window.location.href for immediate redirect
    window.location.href = loginUrl;
  }
};

/**
 * Check if current route is admin route
 */
export const isAdminRoute = (pathname?: string): boolean => {
  if (typeof window !== 'undefined' && !pathname) {
    pathname = window.location.pathname;
  }
  return pathname?.includes('/admin') || false;
};

/**
 * Get appropriate redirect path based on current route
 */
export const getRedirectPath = (storeHandle?: string): string => {
  if (typeof window === 'undefined') {
    return '/login?tab=login';
  }
  
  const currentPath = window.location.pathname;
  
  // If on admin route, redirect to admin login
  if (isAdminRoute(currentPath)) {
    return '/login?tab=login&type=admin';
  }
  
  // If on store route, redirect to store login
  if (storeHandle && currentPath.includes(storeHandle)) {
    return `/${storeHandle}/login`;
  }
  
  // Default to main login
  return '/login?tab=login';
};

/**
 * Enhanced 401 handler with smart redirect
 */
export const handleUnauthorizedWithSmartRedirect = (storeHandle?: string) => {
  console.log('🚨 Smart 401 handler triggered');
  
  const redirectPath = getRedirectPath(storeHandle);
  handle401Unauthorized(storeHandle, redirectPath);
};