'use client';

import { handleUnauthorizedWithSmartRedirect } from './authUtils';

/**
 * Centralized token utility for automatic token retrieval during API calls
 * Supports both store-specific and global authentication tokens
 */

export interface TokenInfo {
  token: string | null;
  source: 'store-auth' | 'global-auth' | 'none';
  storeHandle?: string;
}

/**
 * Get authentication token with automatic fallback logic
 * Priority: store-specific auth > global auth > none
 */
export const getAuthToken = (storeHandle?: string): TokenInfo => {
  if (typeof window === 'undefined') {
    return { token: null, source: 'none' };
  }

  try {
    // 1. Try store-specific auth first if storeHandle is provided
    if (storeHandle) {
      const storeAuthKey = `${storeHandle}-auth`;
      const storeAuthStorage = localStorage.getItem(storeAuthKey);
      
      if (storeAuthStorage) {
        const storeAuthData = JSON.parse(storeAuthStorage);
        const storeToken = storeAuthData.state?.token;
        
        if (storeToken) {
          console.log(`🔐 Found store-specific token for ${storeHandle}`);
          return { 
            token: storeToken, 
            source: 'store-auth', 
            storeHandle 
          };
        }
      }
    }

    // 2. Fallback to global auth storage
    const globalAuthStorage = localStorage.getItem('auth-storage');
    if (globalAuthStorage) {
      const globalAuthData = JSON.parse(globalAuthStorage);
      const globalToken = globalAuthData.state?.token;
      
      if (globalToken) {
        console.log('🔐 Found global auth token');
        return { 
          token: globalToken, 
          source: 'global-auth' 
        };
      }
    }

    // 3. Check legacy token storage
    const legacyToken = localStorage.getItem('ondc_auth_token');
    if (legacyToken) {
      console.log('🔐 Found legacy auth token');
      return { 
        token: legacyToken, 
        source: 'global-auth' 
      };
    }

  } catch (error) {
    console.warn('Error retrieving auth token:', error);
  }

  return { token: null, source: 'none' };
};

/**
 * Get headers with automatic token inclusion
 */
export const getAuthHeaders = (
  storeHandle?: string, 
  additionalHeaders: Record<string, string> = {}
): HeadersInit => {
  const baseHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...additionalHeaders,
  };

  // Add store-specific headers if storeHandle is provided
  if (storeHandle) {
    baseHeaders['x-tenant-id'] = storeHandle;
    baseHeaders['x-publishable-api-key'] = process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || '';
  }

  // Get and add authentication token
  const tokenInfo = getAuthToken(storeHandle);
  if (tokenInfo.token) {
    baseHeaders['Authorization'] = `Bearer ${tokenInfo.token}`;
    console.log(`🔐 Added ${tokenInfo.source} token to headers${tokenInfo.storeHandle ? ` for ${tokenInfo.storeHandle}` : ''}`);
  }

  return baseHeaders;
};

/**
 * Enhanced fetch wrapper with automatic token handling and 401 interceptor
 */
export const fetchWithAuth = async (
  url: string,
  options: RequestInit = {},
  storeHandle?: string
): Promise<Response> => {
  const headers = getAuthHeaders(storeHandle, options.headers as Record<string, string>);
  
  const enhancedOptions: RequestInit = {
    ...options,
    headers,
  };

  console.log(`🌐 Making authenticated request to: ${url}`);
  console.log(`🔐 Using headers:`, Object.keys(headers).reduce((acc, key) => {
    acc[key] = key === 'Authorization' ? 'Bearer [REDACTED]' : headers[key as keyof HeadersInit];
    return acc;
  }, {} as Record<string, any>));

  try {
    const response = await fetch(url, enhancedOptions);
    
    // Check for 401 Unauthorized and handle it
    if (response.status === 401) {
      console.log('🚨 API returned 401 Unauthorized:', url);
      handleUnauthorizedWithSmartRedirect(storeHandle);
    }
    
    return response;
  } catch (error) {
    console.error('❌ Fetch error:', error);
    throw error;
  }
};

/**
 * Check if user is authenticated (has valid token)
 */
export const isAuthenticated = (storeHandle?: string): boolean => {
  const tokenInfo = getAuthToken(storeHandle);
  return tokenInfo.token !== null;
};

/**
 * Get user information from stored auth data
 */
export const getStoredUser = (storeHandle?: string): any | null => {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    // Try store-specific auth first
    if (storeHandle) {
      const storeAuthKey = `${storeHandle}-auth`;
      const storeAuthStorage = localStorage.getItem(storeAuthKey);
      
      if (storeAuthStorage) {
        const storeAuthData = JSON.parse(storeAuthStorage);
        const user = storeAuthData.state?.user;
        
        if (user) {
          return user;
        }
      }
    }

    // Fallback to global auth
    const globalAuthStorage = localStorage.getItem('auth-storage');
    if (globalAuthStorage) {
      const globalAuthData = JSON.parse(globalAuthStorage);
      return globalAuthData.state?.user || null;
    }

  } catch (error) {
    console.warn('Error retrieving stored user:', error);
  }

  return null;
};

/**
 * Clear all authentication data
 * @deprecated Use clearAllAuthData from authUtils instead
 */
export const clearAllAuth = (storeHandle?: string): void => {
  console.warn('clearAllAuth is deprecated, use clearAllAuthData from authUtils instead');
  
  // Import and use the new function
  import('./authUtils').then(({ clearAllAuthData }) => {
    clearAllAuthData(storeHandle);
  }).catch(error => {
    console.error('Error importing authUtils:', error);
    
    // Fallback to old implementation
    if (typeof window === 'undefined') {
      return;
    }

    try {
      // Clear store-specific auth if storeHandle provided
      if (storeHandle) {
        const storeAuthKey = `${storeHandle}-auth`;
        localStorage.removeItem(storeAuthKey);
        console.log(`🧹 Cleared store-specific auth for ${storeHandle}`);
      }

      // Clear global auth
      localStorage.removeItem('auth-storage');
      localStorage.removeItem('ondc_auth_token');
      console.log('🧹 Cleared global auth storage');

    } catch (error) {
      console.warn('Error clearing auth data:', error);
    }
  });
};

/**
 * Token refresh utility (placeholder for future implementation)
 */
export const refreshToken = async (storeHandle?: string): Promise<string | null> => {
  // TODO: Implement token refresh logic when refresh tokens are available
  console.log('🔄 Token refresh not yet implemented');
  return null;
};

/**
 * Validate token format - More lenient validation
 */
export const isValidTokenFormat = (token: string): boolean => {
  if (!token || typeof token !== 'string') {
    return false;
  }
  
  // More lenient validation - accept both JWT and simple tokens
  const trimmedToken = token.trim();
  
  // Must be at least 10 characters and not empty
  if (trimmedToken.length < 10) {
    return false;
  }
  
  // Check for JWT format (3 parts separated by dots)
  const parts = trimmedToken.split('.');
  if (parts.length === 3) {
    // Validate each part is base64-like (contains valid characters)
    return parts.every(part => /^[A-Za-z0-9_-]+$/.test(part));
  }
  
  // Accept other token formats (API keys, simple tokens)
  // Just ensure it contains reasonable characters
  return /^[A-Za-z0-9_.-]+$/.test(trimmedToken);
};

/**
 * Get token expiration info (if JWT)
 */
export const getTokenExpiration = (token: string): Date | null => {
  try {
    if (!isValidTokenFormat(token)) {
      return null;
    }

    const payload = JSON.parse(atob(token.split('.')[1]));
    if (payload.exp) {
      return new Date(payload.exp * 1000);
    }
  } catch (error) {
    console.warn('Error parsing token expiration:', error);
  }
  
  return null;
};

/**
 * Check if token is expired - More lenient checking
 */
export const isTokenExpired = (token: string): boolean => {
  try {
    const expiration = getTokenExpiration(token);
    if (!expiration) {
      // If we can't determine expiration, assume it's valid
      // This handles non-JWT tokens gracefully
      return false;
    }
    
    // Add a small buffer (30 seconds) to account for clock skew
    const now = new Date();
    const expirationWithBuffer = new Date(expiration.getTime() - 30000);
    
    return now >= expirationWithBuffer;
  } catch (error) {
    console.warn('Error checking token expiration:', error);
    // On error, assume token is valid to avoid false positives
    return false;
  }
};