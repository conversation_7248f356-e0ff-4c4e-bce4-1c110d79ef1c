import axios from '../axios';
import { getAuthHeaders, fetchWithAuth } from '@/lib/utils/tokenUtils';
import { handleUnauthorizedWithSmartRedirect } from '@/lib/utils/authUtils';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  access_token?: string;
  token?: string;
  accessToken?: string;
  authToken?: string;
  jwt?: string;
  user?: {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
  };
  // Allow any additional fields that might be in the response
  [key: string]: any;
}

export interface SignupRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  storeName: string;
  storeHandle: string;
}

export interface SignupResponse {
  user: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  store: {
    id: string;
    name: string;
    handle: string;
  };
  message?: string;
}

export interface UserDetailsResponse {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  role?: string;
  created_at?: string;
  updated_at?: string;
  metadata?: {
    onboarding_status?: 'completed' | 'pending';
    store_handle?: string;
    profile_image?: string;
    contact_number?: string;
    bio?: string;
    [key: string]: any;
  };
  store_handle?: string;
  onboarding_status?: 'completed' | 'pending';
  [key: string]: any;
}

export interface UserUpdateRequest {
  first_name: string;
  last_name: string;
  metadata?: {
    profile_image?: string;
    contact_number?: string;
    bio?: string;
    [key: string]: any;
  };
}

export interface UserUpdateResponse {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  metadata?: {
    profile_image?: string;
    contact_number?: string;
    bio?: string;
    [key: string]: any;
  };
  updated_at: string;
  [key: string]: any;
}

export const authApi = {
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    try {
      console.log('=== LOGIN API CALL ===');
      console.log('Making login request to:', '/auth/user/emailpass');
      console.log('Axios instance baseURL:', axios.defaults.baseURL);
      console.log('Full URL will be:', `${axios.defaults.baseURL || 'http://localhost:9000'}/auth/user/emailpass`);
      console.log('Request payload:', credentials);
      
      const response = await axios.post('/auth/user/emailpass', credentials);
      
      console.log('=== LOGIN API RESPONSE ===');
      console.log('Response status:', response.status);
      console.log('Response statusText:', response.statusText);
      console.log('Response headers:', response.headers);
      console.log('Response data type:', typeof response.data);
      console.log('Response data:', response.data);
      console.log('Response data JSON:', JSON.stringify(response.data, null, 2));
      
      // Check if response.data exists
      if (!response.data) {
        console.error('No data in response');
        throw new Error('No data received from login API');
      }
      
      return response.data;
    } catch (error: any) {
      console.error('=== LOGIN API ERROR ===');
      console.error('Error type:', typeof error);
      console.error('Error message:', error.message);
      console.error('Error response:', error.response);
      console.error('Error request:', error.request);
      
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
      }
      
      throw error;
    }
  },

  signup: async (userData: SignupRequest): Promise<SignupResponse> => {
    const response = await axios.post('/public/admin-signup', userData);
    return response.data;
  },

  getUserDetails: async (token?: string, storeHandle?: string): Promise<UserDetailsResponse> => {
    console.log('=== GET USER DETAILS API CALL ===');
    console.log('Token provided:', token ? 'yes' : 'no');
    console.log('Store handle:', storeHandle);
    
    try {
      const response = await axios.get('/admin/users/me', {
        headers: getAuthHeaders(storeHandle, token ? { Authorization: `Bearer ${token}` } : {}),
      });
      
      console.log('=== USER DETAILS API RESPONSE ===');
      console.log('Response status:', response.status);
      console.log('Response data:', response.data);
      console.log('Response data keys:', Object.keys(response.data));
      console.log('Response data JSON:', JSON.stringify(response.data, null, 2));
      
      // Check for store handle in different possible locations
      console.log('=== STORE HANDLE DETECTION IN API RESPONSE ===');
      console.log('response.data.store_handle:', response.data.store_handle);
      console.log('response.data.metadata:', response.data.metadata);
      console.log('response.data.metadata?.store_handle:', response.data.metadata?.store_handle);
      console.log('response.data.user:', response.data.user);
      console.log('response.data.user?.metadata:', response.data.user?.metadata);
      console.log('response.data.user?.metadata?.store_handle:', response.data.user?.metadata?.store_handle);
      console.log('response.data.user?.store_handle:', response.data.user?.store_handle);
      
      return response.data;
    } catch (error: any) {
      // Handle 401 specifically - but don't auto-redirect during login process
      if (error.response?.status === 401) {
        console.log('🚨 getUserDetails returned 401 - token may be invalid');
        console.log('Current URL:', typeof window !== 'undefined' ? window.location.href : 'N/A');
        
        // Only auto-redirect if we're not already on the login page
        const isOnLoginPage = typeof window !== 'undefined' && 
          (window.location.pathname === '/login' || window.location.pathname.includes('/login'));
        
        if (!isOnLoginPage) {
          console.log('🚨 Not on login page - clearing auth and redirecting');
          handleUnauthorizedWithSmartRedirect(storeHandle);
        } else {
          console.log('🚨 Already on login page - not redirecting to avoid loop');
        }
      }
      throw error;
    }
  },

  updateOnboardingStatus: async (status: 'pending' | 'completed', token?: string, storeHandle?: string) => {
    console.log('=== UPDATE ONBOARDING STATUS API CALL ===');
    console.log('Token provided:', token ? 'yes' : 'no');
    console.log('Store handle:', storeHandle);
    console.log('Status:', status);
    
    const response = await axios.patch('/admin/users/me', {
      metadata: {
        onboarding_status: status
      }
    }, {
      headers: getAuthHeaders(storeHandle, token ? { Authorization: `Bearer ${token}` } : {}),
    });
    
    console.log('=== ONBOARDING STATUS UPDATE RESPONSE ===');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
    
    return response.data;
  },

  completeOnboarding: async (userId: string, token?: string, storeHandle?: string) => {
    console.log('=== COMPLETE ONBOARDING API CALL ===');
    console.log('User ID:', userId);
    console.log('Token provided:', token ? 'yes' : 'no');
    console.log('Store handle:', storeHandle);
    
    const payload = {
      metadata: {
        onboarding_status: "completed",
        onboarding_store_configuration: true,
        onboarding_add_product: true,
        onboarding_add_bulk_product: false
      }
    };
    
    console.log('Request payload:', payload);
    
    const response = await axios.patch(`/admin/users/${userId}`, payload, {
      headers: getAuthHeaders(storeHandle, token ? { Authorization: `Bearer ${token}` } : {}),
    });
    
    console.log('=== COMPLETE ONBOARDING RESPONSE ===');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
    
    return response.data;
  },

  updateUserProfile: async (userId: string, userData: UserUpdateRequest, token?: string, storeHandle?: string): Promise<UserUpdateResponse> => {
    console.log('=== UPDATE USER PROFILE API CALL ===');
    console.log('User ID:', userId);
    console.log('Token provided:', token ? 'yes' : 'no');
    console.log('Store handle:', storeHandle);
    console.log('User data:', userData);
    
    try {
      const response = await axios.post(`/admin/users/${userId}`, userData, {
        headers: getAuthHeaders(storeHandle, token ? { Authorization: `Bearer ${token}` } : {}),
      });
      
      console.log('=== UPDATE USER PROFILE RESPONSE ===');
      console.log('Response status:', response.status);
      console.log('Response data:', response.data);
      
      return response.data;
    } catch (error: any) {
      console.error('=== UPDATE USER PROFILE ERROR ===');
      console.error('Error type:', typeof error);
      console.error('Error message:', error.message);
      console.error('Error response:', error.response);
      
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
      }
      
      throw error;
    }
  },



  getStoreConfiguration: async (storeHandle: string) => {
    try {
      console.log('=== STRAPI API CALL ===');
      console.log('Fetching store configuration for handle:', storeHandle);
      
      const url = `http://localhost:1337/api/store-configurations?filters[store_handle][$eq]=${storeHandle}`;
      const response = await fetchWithAuth(url, { method: 'GET' }, storeHandle);
      
      console.log('Strapi response status:', response.status);
      console.log('Strapi response ok:', response.ok);
      
      if (!response.ok) {
        throw new Error(`Strapi API error: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('Strapi response data:', data);
      
      return data;
    } catch (error) {
      console.error('Error fetching store configuration:', error);
      throw error;
    }
  },
};

export default authApi;