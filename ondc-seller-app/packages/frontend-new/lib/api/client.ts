// lib/api/client.ts
import { handleUnauthorizedWithSmartRedirect } from '../utils/authUtils';

// Utility function to handle 401 responses
const handleUnauthorizedResponse = (storeHandle?: string) => {
  console.log('🚨 401 Unauthorized detected in API client');
  handleUnauthorizedWithSmartRedirect(storeHandle);
};

export const medusaClient = async <T>(
  endpoint: string,
  options: RequestInit = {},
  storeHandle?: string
): Promise<T> => {
  // Check if API URL is configured
  const apiUrl = process.env.NEXT_PUBLIC_MEDUSA_STORE_API_URL || process.env.NEXT_PUBLIC_MEDUSA_BASE_URL;
  if (!apiUrl) {
    throw new Error("Medusa API URL not configured");
  }

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

  try {
    const res = await fetch(
      `${apiUrl}${endpoint}`,
      {
        ...options,
        headers: {
          "Content-Type": "application/json",
          ...options.headers,
        },
        credentials: "include",
        signal: controller.signal,
      },
    );

    clearTimeout(timeoutId);

    // Handle 401 Unauthorized responses
    if (res.status === 401) {
      console.log('🚨 Medusa API returned 401 Unauthorized:', endpoint);
      handleUnauthorizedResponse(storeHandle);
      
      // Still throw the error for the caller to handle
      const errorMessage = "Authentication failed - redirecting to login";
      throw new Error(errorMessage);
    }

    if (!res.ok) {
      let errorMessage = "Medusa API error";
      try {
        const error = await res.json();
        errorMessage = error.message || `HTTP ${res.status}: ${res.statusText}`;
      } catch {
        errorMessage = `HTTP ${res.status}: ${res.statusText}`;
      }
      throw new Error(errorMessage);
    }

    return res.json();
  } catch (error) {
    clearTimeout(timeoutId);
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Request timeout - API took too long to respond');
    }
    throw error;
  }
};

export const strapiClient = async <T>(
  endpoint: string,
  options: RequestInit = {},
  storeHandle?: string
): Promise<T> => {
  // Check if API URL is configured
  const apiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || process.env.NEXT_PUBLIC_STRAPI_BASE_URL;
  if (!apiUrl) {
    throw new Error("Strapi API URL not configured");
  }

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

  try {
    const res = await fetch(
      `${apiUrl}${endpoint}`,
      {
        ...options,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_API_TOKEN}`,
          ...options.headers,
        },
        signal: controller.signal,
      },
    );

    clearTimeout(timeoutId);

    // Handle 401 Unauthorized responses
    if (res.status === 401) {
      console.log('🚨 Strapi API returned 401 Unauthorized:', endpoint);
      handleUnauthorizedResponse(storeHandle);
      
      // Still throw the error for the caller to handle
      const errorMessage = "Authentication failed - redirecting to login";
      throw new Error(errorMessage);
    }

    if (!res.ok) {
      let errorMessage = "Strapi API error";
      try {
        const error = await res.json();
        errorMessage = error.message || `HTTP ${res.status}: ${res.statusText}`;
      } catch {
        errorMessage = `HTTP ${res.status}: ${res.statusText}`;
      }
      throw new Error(errorMessage);
    }

    return res.json();
  } catch (error) {
    clearTimeout(timeoutId);
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Request timeout - API took too long to respond');
    }
    throw error;
  }
};
