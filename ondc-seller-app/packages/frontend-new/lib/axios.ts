// lib/axios.ts
import axios from "axios";
import { handleUnauthorizedWithSmartRedirect } from './utils/authUtils';

const baseURL =
  process.env.NEXT_PUBLIC_MEDUSA_BASE_URL || "http://localhost:9000";

const instance = axios.create({
  baseURL,
  headers: {
    "Content-Type": "application/json",
  },
});

instance.interceptors.request.use((config) => {
  if (typeof window !== "undefined") {
    const token = localStorage.getItem("ondc_auth_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }
  return config;
});

// Add response interceptor for 401 handling
instance.interceptors.response.use(
  (response) => {
    // Return successful responses as-is
    return response;
  },
  (error) => {
    // Handle 401 Unauthorized responses
    if (error.response?.status === 401) {
      console.log('🚨 Main Axios instance returned 401 Unauthorized:', error.config?.url);
      
      // Extract store handle from headers if available
      const storeHandle = error.config?.headers?.['x-tenant-id'];
      
      // Clear auth and redirect
      handleUnauthorizedWithSmartRedirect(storeHandle);
    }
    
    // Re-throw the error for the calling code to handle
    return Promise.reject(error);
  }
);

export default instance;
