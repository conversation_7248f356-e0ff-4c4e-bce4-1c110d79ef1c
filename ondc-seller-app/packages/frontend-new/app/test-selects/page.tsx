'use client';

import React from 'react';
import { Box, Typography, Tabs, Tab } from '@mui/material';
import { useState } from 'react';
import SelectExamples from '@/components/examples/SelectExamples';
import FormSelectExamples from '@/components/examples/FormSelectExamples';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`select-tabpanel-${index}`}
      aria-labelledby={`select-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `select-tab-${index}`,
    'aria-controls': `select-tabpanel-${index}`,
  };
}

export default function TestSelectsPage() {
  const [value, setValue] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ width: '100%', minHeight: '100vh', bgcolor: 'background.default' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
        <Box sx={{ maxWidth: 1200, mx: 'auto', px: 3 }}>
          <Typography variant="h4" sx={{ py: 3, fontWeight: 600 }}>
            Select Components Demo
          </Typography>
          <Tabs value={value} onChange={handleChange} aria-label="select components demo tabs">
            <Tab label="Basic Examples" {...a11yProps(0)} />
            <Tab label="Form Integration" {...a11yProps(1)} />
          </Tabs>
        </Box>
      </Box>
      
      <TabPanel value={value} index={0}>
        <SelectExamples />
      </TabPanel>
      
      <TabPanel value={value} index={1}>
        <FormSelectExamples />
      </TabPanel>
    </Box>
  );
}