'use client';

import React from 'react';
import { Box, Typography, Container } from '@mui/material';
import LexicalEditorDemo from '@/components/examples/LexicalEditorDemo';

export default function TestLexicalPage() {
  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default', py: 3 }}>
      <Container maxWidth="xl">
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h3" component="h1" sx={{ fontWeight: 700, mb: 2 }}>
            Lexical Content Editor
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Rich text editing for product content with advanced formatting capabilities
          </Typography>
        </Box>
        
        <LexicalEditorDemo />
      </Container>
    </Box>
  );
}