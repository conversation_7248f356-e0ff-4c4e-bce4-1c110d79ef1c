'use client';

import { useRouter } from 'next/navigation';
import { useCallback } from 'react';

export const useNavigationWithLoading = () => {
  const router = useRouter();

  const navigateWithLoading = useCallback((url: string) => {
    // Use the same custom events as sidebar navigation
    window.dispatchEvent(new CustomEvent('admin-navigation-start'));
    
    // Navigate
    router.push(url);
    
    // Fallback to stop loading after timeout (same as sidebar)
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('admin-navigation-end'));
    }, 3000);
  }, [router]);

  const navigateToAddProduct = useCallback((storeHandle: string) => {
    navigateWithLoading(`/${storeHandle}/admin/products/new`);
  }, [navigateWithLoading]);

  const navigateToEditProduct = useCallback((storeHandle: string, productId: string) => {
    navigateWithLoading(`/${storeHandle}/admin/products/${productId}`);
  }, [navigateWithLoading]);

  const navigateToProducts = useCallback((storeHandle: string) => {
    navigateWithLoading(`/${storeHandle}/admin/products`);
  }, [navigateWithLoading]);

  return {
    navigateWithLoading,
    navigateToAddProduct,
    navigateToEditProduct,
    navigateToProducts,
  };
};