{"name": "@ondc-seller/frontend-new", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^3.3.2", "@mui/icons-material": "^7.3.1", "@mui/material": "^7.3.1", "@tanstack/react-query": "^5.17.0", "date-fns": "^4.1.0", "lexical": "^0.34.0", "next": "14.0.4", "node-vibrant": "^4.0.3", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "react-multi-carousel": "^2.8.6", "recharts": "^3.1.2", "zod": "^3.22.4", "zustand": "^4.4.7"}, "optionalDependencies": {"quill": "^1.3.7", "react-dropzone": "^14.3.8", "react-quill": "^2.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}